{"buildCommand": "prisma generate && pnpm run build:vercel", "installCommand": "pnpm install", "framework": "nextjs", "regions": ["hkg1"], "outputDirectory": ".next", "functions": {"src/app/api/**/*.ts": {"runtime": "nodejs20.x"}}, "env": {"NEXTAUTH_URL": "https://${VERCEL_URL}", "VERCEL_URL": "${VERCEL_URL}", "SKIP_ENV_VALIDATION": "1"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}