var y=()=>{};y.prototype=y;let _;function O(e){_=e}let f=0,m=null;function h(){return(m===null||m.byteLength===0)&&(m=new Uint8Array(_.memory.buffer)),m}const E=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let S=new E("utf-8");const F=typeof S.encodeInto=="function"?function(e,t){return S.encodeInto(e,t)}:function(e,t){const n=S.encode(e);return t.set(n),{read:e.length,written:n.length}};function l(e,t,n){if(n===void 0){const i=S.encode(e),g=t(i.length,1)>>>0;return h().subarray(g,g+i.length).set(i),f=i.length,g}let r=e.length,o=t(r,1)>>>0;const u=h();let c=0;for(;c<r;c++){const i=e.charCodeAt(c);if(i>127)break;u[o+c]=i}if(c!==r){c!==0&&(e=e.slice(c)),o=n(o,r,r=c+e.length*3,1)>>>0;const i=h().subarray(o+c,o+r),g=F(e,i);c+=g.written,o=n(o,r,c,1)>>>0}return f=c,o}let a=null;function s(){return(a===null||a.buffer.detached===!0||a.buffer.detached===void 0&&a.buffer!==_.memory.buffer)&&(a=new DataView(_.memory.buffer)),a}function w(e){const t=_.__externref_table_alloc();return _.__wbindgen_export_4.set(t,e),t}function d(e,t){try{return e.apply(this,t)}catch(n){const r=w(n);_.__wbindgen_exn_store(r)}}const j=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let A=new j("utf-8",{ignoreBOM:!0,fatal:!0});A.decode();function p(e,t){return e=e>>>0,A.decode(h().subarray(e,e+t))}function b(e){return e==null}function T(e){const t=typeof e;if(t=="number"||t=="boolean"||e==null)return`${e}`;if(t=="string")return`"${e}"`;if(t=="symbol"){const o=e.description;return o==null?"Symbol":`Symbol(${o})`}if(t=="function"){const o=e.name;return typeof o=="string"&&o.length>0?`Function(${o})`:"Function"}if(Array.isArray(e)){const o=e.length;let u="[";o>0&&(u+=T(e[0]));for(let c=1;c<o;c++)u+=", "+T(e[c]);return u+="]",u}const n=/\[object ([^\]]+)\]/.exec(toString.call(e));let r;if(n&&n.length>1)r=n[1];else return toString.call(e);if(r=="Object")try{return"Object("+JSON.stringify(e)+")"}catch{return"Object"}return e instanceof Error?`${e.name}: ${e.message}
${e.stack}`:r}function x(e){const t=_.__wbindgen_export_4.get(e);return _.__externref_table_dealloc(e),t}const I=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>_.__wbg_querycompiler_free(e>>>0,1));class q{__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,I.unregister(this),t}free(){const t=this.__destroy_into_raw();_.__wbg_querycompiler_free(t,0)}constructor(t){const n=_.querycompiler_new(t);if(n[2])throw x(n[1]);return this.__wbg_ptr=n[0]>>>0,I.register(this,this.__wbg_ptr,this),this}compile(t){const n=l(t,_.__wbindgen_malloc,_.__wbindgen_realloc),r=f,o=_.querycompiler_compile(this.__wbg_ptr,n,r);if(o[2])throw x(o[1]);return x(o[0])}compileBatch(t){const n=l(t,_.__wbindgen_malloc,_.__wbindgen_realloc),r=f,o=_.querycompiler_compileBatch(this.__wbg_ptr,n,r);if(o[2])throw x(o[1]);return x(o[0])}}function M(e,t){const n=String(t),r=l(n,_.__wbindgen_malloc,_.__wbindgen_realloc),o=f;s().setInt32(e+4*1,o,!0),s().setInt32(e+4*0,r,!0)}function U(e){return e.buffer}function B(){return d(function(e,t){return e.call(t)},arguments)}function L(){return d(function(e,t,n){return e.call(t,n)},arguments)}function N(e){return e.crypto}function R(e){return Object.entries(e)}function k(){return d(function(e,t){e.getRandomValues(t)},arguments)}function C(e){return e.getTime()}function $(e,t){return e[t>>>0]}function V(e,t){return e[t]}function W(e){let t;try{t=e instanceof ArrayBuffer}catch{t=!1}return t}function z(e){let t;try{t=e instanceof Uint8Array}catch{t=!1}return t}function G(e){return Number.isSafeInteger(e)}function P(e){return Object.keys(e)}function Q(e){return e.length}function H(e){return e.length}function J(e){return e.msCrypto}function Y(){return new Date}function K(){return new Object}function X(){return new Map}function Z(){return new Array}function v(e){return new Uint8Array(e)}function ee(e,t){return new y(p(e,t))}function te(e,t,n){return new Uint8Array(e,t>>>0,n>>>0)}function ne(e){return new Uint8Array(e>>>0)}function re(e){return e.node}function oe(){return Date.now()}function _e(){return d(function(){return Date.now()},arguments)}function ce(e){return e.process}function ie(){return d(function(e,t){e.randomFillSync(t)},arguments)}function ue(){return d(function(){return module.require},arguments)}function se(e,t,n){e[t>>>0]=n}function fe(e,t,n){e[t]=n}function be(e,t,n){e.set(t,n>>>0)}function ae(e,t,n){return e.set(t,n)}function de(e,t){global.PRISMA_WASM_PANIC_REGISTRY.set_message(p(e,t))}function ge(){const e=typeof global>"u"?null:global;return b(e)?0:w(e)}function le(){const e=typeof globalThis>"u"?null:globalThis;return b(e)?0:w(e)}function we(){const e=typeof self>"u"?null:self;return b(e)?0:w(e)}function pe(){const e=typeof window>"u"?null:window;return b(e)?0:w(e)}function xe(e,t,n){return e.subarray(t>>>0,n>>>0)}function ye(e){return e.versions}function me(e){return+e}function he(e){return e}function Se(e){return BigInt.asUintN(64,e)}function Te(e){const t=e;return typeof t=="boolean"?t?1:0:2}function Ae(e,t){const n=T(t),r=l(n,_.__wbindgen_malloc,_.__wbindgen_realloc),o=f;s().setInt32(e+4*1,o,!0),s().setInt32(e+4*0,r,!0)}function Ie(e,t){return new Error(p(e,t))}function Ee(e,t){return e in t}function Fe(){const e=_.__wbindgen_export_4,t=e.grow(4);e.set(0,void 0),e.set(t+0,void 0),e.set(t+1,null),e.set(t+2,!0),e.set(t+3,!1)}function je(e){return typeof e=="function"}function De(e){const t=e;return typeof t=="object"&&t!==null}function Oe(e){return typeof e=="string"}function qe(e){return e===void 0}function Me(e,t){return e==t}function Ue(){return _.memory}function Be(e,t){const n=t,r=typeof n=="number"?n:void 0;s().setFloat64(e+8*1,b(r)?0:r,!0),s().setInt32(e+4*0,!b(r),!0)}function Le(e){return e}function Ne(e,t){const n=t,r=typeof n=="string"?n:void 0;var o=b(r)?0:l(r,_.__wbindgen_malloc,_.__wbindgen_realloc),u=f;s().setInt32(e+4*1,u,!0),s().setInt32(e+4*0,o,!0)}function Re(e,t){return p(e,t)}function ke(e,t){throw new Error(p(e,t))}export{q as QueryCompiler,M as __wbg_String_8f0eb39a4a4c2f66,U as __wbg_buffer_609cc3eee51ed158,B as __wbg_call_672a4d21634d4a24,L as __wbg_call_7cccdd69e0791ae2,N as __wbg_crypto_805be4ce92f1e370,R as __wbg_entries_3265d4158b33e5dc,k as __wbg_getRandomValues_f6a868620c8bab49,C as __wbg_getTime_46267b1c24877e30,$ as __wbg_get_b9b93047fe3cf45b,V as __wbg_getwithrefkey_1dc361bd10053bfe,W as __wbg_instanceof_ArrayBuffer_e14585432e3737fc,z as __wbg_instanceof_Uint8Array_17156bcf118086a9,G as __wbg_isSafeInteger_343e2beeeece1bb0,P as __wbg_keys_5c77a08ddc2fb8a6,Q as __wbg_length_a446193dc22c12f8,H as __wbg_length_e2d2a49132c1b256,J as __wbg_msCrypto_2ac4d17c4748234a,Y as __wbg_new0_f788a2397c7ca929,K as __wbg_new_405e22f390576ce2,X as __wbg_new_5e0be73521bc8c17,Z as __wbg_new_78feb108b6472713,v as __wbg_new_a12002a7f91c75be,ee as __wbg_newnoargs_105ed471475aaf50,te as __wbg_newwithbyteoffsetandlength_d97e637ebe145a9a,ne as __wbg_newwithlength_a381634e90c276d4,re as __wbg_node_ecc8306b9857f33d,oe as __wbg_now_807e54c39636c349,_e as __wbg_now_b3f7572f6ef3d3a9,ce as __wbg_process_5cff2739921be718,ie as __wbg_randomFillSync_d3c85af7e31cf1f8,ue as __wbg_require_0c566c6f2eef6c79,se as __wbg_set_37837023f3d740e8,fe as __wbg_set_3f1d0b984ed272ed,be as __wbg_set_65595bdd868b3009,ae as __wbg_set_8fc6bf8a5b1071d1,O as __wbg_set_wasm,de as __wbg_setmessage_82ae475bb413aa5c,ge as __wbg_static_accessor_GLOBAL_88a902d13a557d07,le as __wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0,we as __wbg_static_accessor_SELF_37c5d418e4bf5819,pe as __wbg_static_accessor_WINDOW_5de37043a91a9c40,xe as __wbg_subarray_aa9065fa9dc5df96,ye as __wbg_versions_a8e5a362e1f16442,me as __wbindgen_as_number,he as __wbindgen_bigint_from_i64,Se as __wbindgen_bigint_from_u64,Te as __wbindgen_boolean_get,Ae as __wbindgen_debug_string,Ie as __wbindgen_error_new,Ee as __wbindgen_in,Fe as __wbindgen_init_externref_table,je as __wbindgen_is_function,De as __wbindgen_is_object,Oe as __wbindgen_is_string,qe as __wbindgen_is_undefined,Me as __wbindgen_jsval_loose_eq,Ue as __wbindgen_memory,Be as __wbindgen_number_get,Le as __wbindgen_number_new,Ne as __wbindgen_string_get,Re as __wbindgen_string_new,ke as __wbindgen_throw};
