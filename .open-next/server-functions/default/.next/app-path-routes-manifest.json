{"/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/signup/route": "/api/auth/signup", "/api/orders/activate/route": "/api/orders/activate", "/api/orders/route": "/api/orders", "/api/stripe/route": "/api/stripe", "/api/github/check-permissions/route": "/api/github/check-permissions", "/api/posts/route": "/api/posts", "/api/github/invite/route": "/api/github/invite", "/robots.txt/route": "/robots.txt", "/api/stripe/webhook/route": "/api/stripe/webhook", "/sitemap.xml/route": "/sitemap.xml", "/api/users/route": "/api/users", "/api/analytics/web-vitals/route": "/api/analytics/web-vitals", "/_not-found/page": "/_not-found", "/[locale]/admin/performance/page": "/[locale]/admin/performance", "/[locale]/demo/github-invite/page": "/[locale]/demo/github-invite", "/[locale]/orders/page": "/[locale]/orders", "/[locale]/demo/multi-plan-github/page": "/[locale]/demo/multi-plan-github", "/[locale]/demo/github-permissions/page": "/[locale]/demo/github-permissions", "/[locale]/page": "/[locale]", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/auth/signin/page": "/[locale]/auth/signin"}