{"/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/signup/route": "/api/auth/signup", "/api/github/check-permissions/route": "/api/github/check-permissions", "/api/github/invite/route": "/api/github/invite", "/api/orders/activate/route": "/api/orders/activate", "/api/orders/route": "/api/orders", "/api/posts/route": "/api/posts", "/api/stripe/route": "/api/stripe", "/api/stripe/webhook/route": "/api/stripe/webhook", "/api/users/route": "/api/users", "/robots.txt/route": "/robots.txt", "/sitemap.xml/route": "/sitemap.xml", "/api/analytics/web-vitals/route": "/api/analytics/web-vitals", "/_not-found/page": "/_not-found", "/[locale]/demo/github-permissions/page": "/[locale]/demo/github-permissions", "/[locale]/admin/performance/page": "/[locale]/admin/performance", "/[locale]/demo/github-invite/page": "/[locale]/demo/github-invite", "/[locale]/demo/multi-plan-github/page": "/[locale]/demo/multi-plan-github", "/[locale]/page": "/[locale]", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/auth/signin/page": "/[locale]/auth/signin", "/[locale]/orders/page": "/[locale]/orders"}