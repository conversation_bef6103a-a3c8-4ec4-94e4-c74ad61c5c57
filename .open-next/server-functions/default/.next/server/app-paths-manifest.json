{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/signup/route": "app/api/auth/signup/route.js", "/api/orders/activate/route": "app/api/orders/activate/route.js", "/api/orders/route": "app/api/orders/route.js", "/api/stripe/route": "app/api/stripe/route.js", "/api/github/check-permissions/route": "app/api/github/check-permissions/route.js", "/api/posts/route": "app/api/posts/route.js", "/api/github/invite/route": "app/api/github/invite/route.js", "/robots.txt/route": "app/robots.txt/route.js", "/api/stripe/webhook/route": "app/api/stripe/webhook/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/users/route": "app/api/users/route.js", "/api/analytics/web-vitals/route": "app/api/analytics/web-vitals/route.js", "/_not-found/page": "app/_not-found/page.js", "/[locale]/admin/performance/page": "app/[locale]/admin/performance/page.js", "/[locale]/demo/github-invite/page": "app/[locale]/demo/github-invite/page.js", "/[locale]/orders/page": "app/[locale]/orders/page.js", "/[locale]/demo/multi-plan-github/page": "app/[locale]/demo/multi-plan-github/page.js", "/[locale]/demo/github-permissions/page": "app/[locale]/demo/github-permissions/page.js", "/[locale]/page": "app/[locale]/page.js", "/[locale]/profile/page": "app/[locale]/profile/page.js", "/[locale]/auth/signin/page": "app/[locale]/auth/signin/page.js"}