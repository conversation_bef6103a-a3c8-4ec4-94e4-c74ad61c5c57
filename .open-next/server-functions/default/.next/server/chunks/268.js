exports.id=268,exports.ids=[268],exports.modules={2460:(e,t,n)=>{"use strict";n.d(t,{ServiceWorkerRegistration:()=>i});var o=n(57479);let i=(0,o.registerClientReference)(function(){throw Error("Attempted to call ServiceWorkerRegistration() from the server but ServiceWorkerRegistration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ServiceWorkerRegistration.tsx","ServiceWorkerRegistration");(0,o.registerClientReference)(function(){throw Error("Attempted to call updateServiceWorker() from the server but updateServiceWorker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ServiceWorkerRegistration.tsx","updateServiceWorker"),(0,o.registerClientReference)(function(){throw Error("Attempted to call getCacheStatus() from the server but getCacheStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ServiceWorkerRegistration.tsx","getCacheStatus"),(0,o.registerClientReference)(function(){throw Error("Attempted to call clearAllCaches() from the server but clearAllCaches is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ServiceWorkerRegistration.tsx","clearAllCaches")},4674:(e,t,n)=>{"use strict";function o(){return null}n.d(t,{ServiceWorkerRegistration:()=>o}),n(26926)},4740:(e,t,n)=>{"use strict";n.d(t,{WebVitals:()=>i});var o=n(38073);function i(){return(0,o.useReportWebVitals)(e=>{switch(fetch("/api/analytics/web-vitals",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}).catch(console.error),e.name){case"FCP":case"LCP":case"CLS":case"FID":case"TTFB":case"INP":e.value}}),null}},4904:(e,t,n)=>{Promise.resolve().then(n.bind(n,13624)),Promise.resolve().then(n.bind(n,86434)),Promise.resolve().then(n.bind(n,2460))},5284:(e,t,n)=>{Promise.resolve().then(n.bind(n,31625))},8143:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>h,metadata:()=>d,viewport:()=>c});var o=n(21393),i=n(8908),r=n.n(i);n(23599);var s=n(86434),a=n(13624),l=n(2460);let c={width:"device-width",initialScale:1,maximumScale:5,userScalable:!0,themeColor:[{media:"(prefers-color-scheme: light)",color:"white"},{media:"(prefers-color-scheme: dark)",color:"black"}]},d={metadataBase:new URL(process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"),title:{default:"NextLaunchPad",template:"%s | NextLaunchPad"},description:"A modern Next.js 15 SaaS template with authentication, payments, and internationalization",keywords:["Next.js","React","TypeScript","SaaS","Template","Authentication","Payments","Internationalization","Tailwind CSS"],authors:[{name:"NextLaunchPad Team",url:"https://github.com/nextlaunchpad"}],creator:"NextLaunchPad Team",openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000",title:"NextLaunchPad",description:"A modern Next.js 15 SaaS template with authentication, payments, and internationalization",siteName:"NextLaunchPad",images:[{url:"/og-image.png",width:1200,height:630,alt:"NextLaunchPad - Modern SaaS Template"}]},twitter:{card:"summary_large_image",title:"NextLaunchPad",description:"A modern Next.js 15 SaaS template with authentication, payments, and internationalization",images:["/og-image.png"],creator:"@nextlaunchpad"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},manifest:"/manifest.json",icons:{icon:"/favicon.ico"}};function h({children:e}){return(0,o.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,o.jsxs)("head",{children:[(0,o.jsx)("link",{rel:"preconnect",href:"https://images.unsplash.com"}),(0,o.jsx)("link",{rel:"preconnect",href:"https://unsplash.com"}),(0,o.jsx)("link",{rel:"dns-prefetch",href:"https://fonts.googleapis.com"}),(0,o.jsx)("link",{rel:"dns-prefetch",href:"https://fonts.gstatic.com"}),(0,o.jsx)("link",{rel:"preconnect",href:"https://api.stripe.com"}),(0,o.jsx)("link",{rel:"dns-prefetch",href:"https://js.stripe.com"})]}),(0,o.jsxs)("body",{className:`${r().variable} font-sans antialiased`,suppressHydrationWarning:!0,children:[(0,o.jsx)(a.PerformanceInit,{}),(0,o.jsx)(s.WebVitals,{}),(0,o.jsx)(l.ServiceWorkerRegistration,{}),e]})]})}},13624:(e,t,n)=>{"use strict";n.d(t,{PerformanceInit:()=>o});let o=(0,n(57479).registerClientReference)(function(){throw Error("Attempted to call PerformanceInit() from the server but PerformanceInit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/performance-init.tsx","PerformanceInit")},23599:()=>{},23810:(e,t,n)=>{"use strict";n.d(t,{PerformanceInit:()=>o}),n(26926);function o(){return null}console.warn,console.error,console.debug,console.info;let i=`
  .data-saver-mode img[loading="lazy"] {
    background: #f0f0f0;
  }
  
  .data-saver-mode video {
    display: none;
  }
  
  .data-saver-mode .animation {
    animation: none !important;
  }
  
  .data-saver-mode .parallax {
    transform: none !important;
  }
`;if("undefined"!=typeof document){let e=document.createElement("style");e.textContent=i,document.head.appendChild(e)}},29639:()=>{},31625:(e,t,n)=>{"use strict";n.d(t,{NotFound:()=>p});var o=n(43147),i=n(66142),r=n(14394),s=n.n(r),a=n(71787),l=n(62948);let c={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.7,delayChildren:.1,staggerChildren:.1}}},d={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6}}},h={hidden:e=>({opacity:0,x:40*e,y:15,rotate:5*e}),visible:{opacity:.7,x:0,y:0,rotate:0,transition:{duration:.8}}},m={hidden:{scale:.8,opacity:0,y:15,rotate:-5},visible:{scale:1,opacity:1,y:0,rotate:0,transition:{duration:.6}},hover:{scale:1.1,y:-10,rotate:[0,-5,5,-5,0],transition:{duration:.8,rotate:{duration:2,repeat:1/0,repeatType:"reverse"}}},floating:{y:[-5,5],transition:{y:{duration:2,repeat:1/0,repeatType:"reverse"}}}};function p(){return(0,o.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center bg-white px-4",children:(0,o.jsx)(a.N,{mode:"wait",children:(0,o.jsxs)(l.P.div,{className:"text-center",variants:c,initial:"hidden",animate:"visible",exit:"hidden",children:[(0,o.jsxs)("div",{className:"flex items-center justify-center gap-4 md:gap-6 mb-8 md:mb-12",children:[(0,o.jsx)(l.P.span,{className:"text-[80px] md:text-[120px] font-bold text-[#222222] opacity-70 font-signika select-none",variants:h,custom:-1,children:"4"}),(0,o.jsx)(l.P.div,{variants:m,whileHover:"hover",animate:["visible","floating"],children:(0,o.jsx)(i.default,{src:"/404.png",alt:"Ghost",width:120,height:120,className:"w-[80px] h-[80px] md:w-[120px] md:h-[120px] object-contain select-none",draggable:"false",priority:!0})}),(0,o.jsx)(l.P.span,{className:"text-[80px] md:text-[120px] font-bold text-[#222222] opacity-70 font-signika select-none",variants:h,custom:1,children:"4"})]}),(0,o.jsx)(l.P.h1,{className:"text-3xl md:text-5xl font-bold text-[#222222] mb-4 md:mb-6 opacity-70 font-dm-sans select-none",variants:d,children:"Boo! Page missing!"}),(0,o.jsx)(l.P.p,{className:"text-lg md:text-xl text-[#222222] mb-8 md:mb-12 opacity-50 font-dm-sans select-none",variants:d,children:"Whoops! This page must be a ghost - it's not here!"}),(0,o.jsx)(l.P.div,{variants:d,whileHover:{scale:1.05,transition:{duration:.3}},children:(0,o.jsx)(s(),{href:"/",className:"inline-block bg-[#222222] text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-[#000000] transition-colors font-dm-sans select-none",children:"Back to Home"})})]})})})}},59903:()=>{},68456:(e,t,n)=>{Promise.resolve().then(n.bind(n,23810)),Promise.resolve().then(n.bind(n,4740)),Promise.resolve().then(n.bind(n,4674))},73693:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});var o=n(21393),i=n(92971);function r(){return(0,o.jsx)("div",{className:"min-h-screen w-full bg-white",children:(0,o.jsx)(i.NotFound,{})})}n(23599)},73980:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,63808,23)),Promise.resolve().then(n.t.bind(n,47302,23)),Promise.resolve().then(n.t.bind(n,66550,23)),Promise.resolve().then(n.t.bind(n,3585,23)),Promise.resolve().then(n.t.bind(n,45077,23)),Promise.resolve().then(n.t.bind(n,90253,23)),Promise.resolve().then(n.t.bind(n,66805,23)),Promise.resolve().then(n.t.bind(n,42631,23))},75428:(e,t,n)=>{Promise.resolve().then(n.bind(n,92971))},83708:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,5950,23)),Promise.resolve().then(n.t.bind(n,82752,23)),Promise.resolve().then(n.t.bind(n,74260,23)),Promise.resolve().then(n.t.bind(n,44095,23)),Promise.resolve().then(n.t.bind(n,55687,23)),Promise.resolve().then(n.t.bind(n,90847,23)),Promise.resolve().then(n.t.bind(n,1743,23)),Promise.resolve().then(n.t.bind(n,37089,23))},86434:(e,t,n)=>{"use strict";n.d(t,{WebVitals:()=>o});let o=(0,n(57479).registerClientReference)(function(){throw Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/analytics/WebVitals.tsx","WebVitals")},92971:(e,t,n)=>{"use strict";n.d(t,{NotFound:()=>o});let o=(0,n(57479).registerClientReference)(function(){throw Error("Attempted to call NotFound() from the server but NotFound is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ui/ghost-404-page.tsx","NotFound")}};