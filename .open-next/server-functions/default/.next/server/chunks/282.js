"use strict";exports.id=282,exports.ids=[282],exports.modules={8243:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(91164).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8818:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(91164).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},73282:(e,t,s)=>{s.d(t,{a:()=>w});var a=s(43147),i=s(26926),r=s(56783),n=s(25431),l=s(41502),d=s(8900),o=s(14565),c=s(88056);let m=d.bL;d.l9;let u=d.ZL;d.bm;let x=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(d.hJ,{ref:s,className:(0,c.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));x.displayName=d.hJ.displayName;let h=i.forwardRef(({className:e,children:t,...s},i)=>(0,a.jsxs)(u,{children:[(0,a.jsx)(x,{}),(0,a.jsxs)(d.UC,{ref:i,className:(0,c.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(d.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));h.displayName=d.UC.displayName;let g=({className:e,...t})=>(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});g.displayName="DialogHeader";let p=({className:e,...t})=>(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});p.displayName="DialogFooter";let b=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(d.hE,{ref:s,className:(0,c.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));b.displayName=d.hE.displayName;let f=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(d.VY,{ref:s,className:(0,c.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=d.VY.displayName;var j=s(8818),v=s(90770),y=s(8243),N=s(53119),k=s(67880);function w({isOpen:e,onClose:t,orderNo:s,productName:d}){let o=(0,k.c3)("orders"),[c,u]=(0,i.useState)(""),[x,w]=(0,i.useState)(!1),[I,A]=(0,i.useState)(""),[C,S]=(0,i.useState)(!1),[z,R]=(0,i.useState)(""),[F,U]=(0,i.useState)(""),$=async e=>{if(e.preventDefault(),!c.trim())return void A(o("githubInvite.usernameRequired"));if(!/^[a-z\d](?:[a-z\d]|-(?=[a-z\d])){0,38}$/i.test(c.trim()))return void A(o("githubInvite.invalidUsername"));w(!0),A("");try{let e=await fetch("/api/github/invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderNo:s,githubUsername:c.trim()})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to send invitation");S(!0),R(t.invitationUrl||""),U(t.repositoryName||""),setTimeout(()=>{window.open("https://github.com/notifications","_blank")},2e3)}catch(t){let e=t.message||o("githubInvite.sendError");t.message.includes("GitHub username not found")?e=o("githubInvite.usernameNotFound"):t.message.includes("Insufficient permissions")?e=o("githubInvite.insufficientPermissions"):t.message.includes("already be a collaborator")&&(e=o("githubInvite.alreadyCollaborator")),A(e)}finally{w(!1)}},E=()=>{u(""),A(""),S(!1),R(""),U(""),t()};return(0,a.jsx)(m,{open:e,onOpenChange:E,children:(0,a.jsxs)(h,{className:"sm:max-w-md",children:[(0,a.jsxs)(g,{children:[(0,a.jsxs)(b,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"w-5 h-5"}),o("githubInvite.title")]}),(0,a.jsx)(f,{children:o("githubInvite.description",{productName:d})})]}),C?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:[(0,a.jsx)(N.A,{className:"w-6 h-6 text-green-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-green-800 dark:text-green-200",children:o("githubInvite.successTitle")}),(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:o("githubInvite.successMessage",{username:c})}),F&&(0,a.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1",children:["Repository: ShipSaaSCo/",F]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:o("githubInvite.nextSteps")}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2",children:o("githubInvite.autoRedirect")}),(0,a.jsxs)("ul",{className:"text-xs text-blue-700 dark:text-blue-300 space-y-1 ml-4",children:[(0,a.jsxs)("li",{children:["• ",o("githubInvite.step1")]}),(0,a.jsxs)("li",{children:["• ",o("githubInvite.step2")]}),(0,a.jsxs)("li",{children:["• ",o("githubInvite.step3")]})]})]})]}),(0,a.jsxs)(p,{children:[(0,a.jsx)(r.$,{variant:"outline",onClick:E,children:o("githubInvite.close")}),(0,a.jsxs)(r.$,{onClick:()=>{window.open("https://github.com/notifications","_blank")},children:[(0,a.jsx)(j.A,{className:"w-4 h-4 mr-2"}),o("githubInvite.viewNotifications")]})]})]}):(0,a.jsxs)("form",{onSubmit:$,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"github-username",children:o("githubInvite.usernameLabel")}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)(n.p,{id:"github-username",type:"text",placeholder:o("githubInvite.usernamePlaceholder"),value:c,onChange:e=>u(e.target.value),className:"pl-10",disabled:x})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:o("githubInvite.usernameHint")})]}),I&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 text-red-500"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:I})]}),(0,a.jsxs)(p,{children:[(0,a.jsx)(r.$,{type:"button",variant:"outline",onClick:E,disabled:x,children:o("githubInvite.cancel")}),(0,a.jsx)(r.$,{type:"submit",disabled:x||!c.trim(),className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-2 animate-spin"}),o("githubInvite.sending")]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"w-4 h-4 mr-2"}),o("githubInvite.sendInvite")]})})]})]})]})})}},90770:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(91164).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};