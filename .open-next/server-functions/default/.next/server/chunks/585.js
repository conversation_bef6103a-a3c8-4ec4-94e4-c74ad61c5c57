exports.id=585,exports.ids=[585],exports.modules={1453:(e,t,a)=>{"use strict";a.d(t,{PricingSection:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call PricingSection() from the server but PricingSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/blocks/pricing-section.tsx","PricingSection")},3049:(e,t,a)=>{"use strict";a.d(t,{Benefits:()=>n});var r=a(43147),s=a(62948);function n({section:e}){return(0,r.jsxs)("section",{id:"features",className:"py-24 relative overflow-hidden bg-background",children:[(0,r.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background to-background"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_800px_at_30%_200px,rgba(59,130,246,0.03),transparent)] dark:bg-[radial-gradient(circle_800px_at_30%_200px,rgba(59,130,246,0.06),transparent)]"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_600px_at_70%_300px,rgba(139,92,246,0.02),transparent)] dark:bg-[radial-gradient(circle_600px_at_70%_300px,rgba(139,92,246,0.04),transparent)]"})]}),(0,r.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,r.jsx)("div",{className:"h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(to_bottom,rgba(0,0,0,0.015)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px]"})}),(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300",children:e.title}),(0,r.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed",children:e.subtitle})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.benefits.map((e,t)=>(0,r.jsx)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"group relative",children:(0,r.jsxs)("div",{className:"relative bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300",children:(0,r.jsx)("span",{className:"text-2xl",children:e.icon})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 leading-relaxed",children:e.description})]}),(0,r.jsx)("div",{className:"absolute top-4 right-4 w-2 h-2 bg-blue-400/30 rounded-full group-hover:bg-blue-400/60 transition-colors"}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 w-1 h-1 bg-purple-400/30 rounded-full group-hover:bg-purple-400/60 transition-colors"})]})},t))})]})]})}},5089:(e,t,a)=>{"use strict";a.d(t,{Providers:()=>d});var r=a(43147),s=a(40519),n=a(80763),i=a(96313),o=a(49931);function l({error:e,resetErrorBoundary:t}){return(0,r.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-red-600",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-md",children:e.message||"An unexpected error occurred. Please try again."}),(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Try again"})]})})}function d({children:e}){return(0,r.jsx)(o.tH,{FallbackComponent:l,onError:(e,t)=>{},onReset:()=>{window.location.reload()},children:(0,r.jsx)(s.SessionProvider,{refetchInterval:300,refetchOnWindowFocus:!0,children:(0,r.jsxs)(n.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,enableColorScheme:!0,disableTransitionOnChange:!0,themes:["light","dark","system"],storageKey:"nextlaunchpad-theme",children:[e,(0,r.jsx)(i.l$,{position:"top-right",richColors:!0,closeButton:!0,expand:!1,duration:4e3})]})})})}},10746:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u,generateMetadata:()=>p,generateStaticParams:()=>h});var r=a(21393),s=a(61286),n=a(6778),i=a(99086),o=a(64064);a(23599);var l=a(54155),d=a(67060),c=a(51871),m=a(18775),x=a.n(m);async function h(){return i.DT.locales.map(e=>({locale:e}))}async function p({params:e}){try{let{locale:t}=await e;if(!t||t.includes(".")||"favicon.ico"===t||"manifest.json"===t||"robots.txt"===t||"sitemap.xml"===t||!(0,s.EL)(i.DT.locales,t))return{title:"NextLaunchPad",description:"A modern Next.js SaaS template"};let a=await (0,i.VL)(t);return{title:{template:`%s | ${a.header?.logo||"NextLaunchPad"}`,default:a.header?.logo||"NextLaunchPad"},description:a.hero?.description||"A modern Next.js SaaS template",keywords:["Next.js","React","TypeScript","SaaS","Template","Web Development","AI"],authors:[{name:"NextLaunchPad Team"}],openGraph:{title:a.hero?.title||"NextLaunchPad",description:a.hero?.description||"A modern Next.js SaaS template",type:"website",locale:t,siteName:a.header?.logo||"NextLaunchPad"},alternates:{languages:i.DT.locales.reduce((e,t)=>(e[t]=`/${t}`,e),{})}}}catch(e){return{title:"NextLaunchPad",description:"A modern Next.js SaaS template"}}}async function u({children:e,params:t}){try{let{locale:a}=await t;(!a||a.includes(".")||a.startsWith("_")||"favicon.ico"===a||"manifest.json"===a||"robots.txt"===a||"sitemap.xml"===a)&&(0,o.notFound)(),(0,s.EL)(i.DT.locales,a)||(0,o.notFound)();let[m,h]=await Promise.all([(0,i.VL)(a),(0,d.J)(a)]);return(0,r.jsx)("html",{lang:a,className:"scroll-smooth",suppressHydrationWarning:!0,children:(0,r.jsx)("body",{className:`${x().variable} font-sans antialiased`,suppressHydrationWarning:!0,children:(0,r.jsx)(n.A,{messages:m,locale:a,children:(0,r.jsxs)(c.Providers,{children:[h.header&&(0,r.jsx)(l.xw,{header:h.header}),(0,r.jsx)("main",{className:"flex-1 min-h-screen",children:e})]})})})})}catch(e){(0,o.notFound)()}}},11846:(e,t,a)=>{"use strict";a.d(t,{Footer:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/Footer.tsx","Footer")},12413:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/LandingHeader.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/LandingHeader.tsx","default")},18421:(e,t,a)=>{var r={"./en.json":[50920,920],"./zh.json":[86113,113]};function s(e){if(!a.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],s=t[0];return a.e(t[1]).then(()=>a.t(s,19))}s.keys=()=>Object.keys(r),s.id=18421,e.exports=s},24750:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(428),s=a(62865);function n(...e){return(0,s.QP)((0,r.$)(e))}},25431:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var r=a(43147),s=a(26926),n=a(88056);let i=s.forwardRef(({className:e,type:t,...a},s)=>(0,r.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));i.displayName="Input"},28669:(e,t,a)=>{"use strict";a.d(t,{Hero:()=>i});var r=a(43147),s=a(56783),n=a(62948);function i({hero:e}){return(0,r.jsxs)("section",{id:"hero",className:"relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-16 md:py-24 lg:py-32 overflow-hidden bg-background","aria-label":"Hero section",children:[(0,r.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background/95 to-background"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_600px_at_50%_300px,rgba(99,102,241,0.05),transparent)] dark:bg-[radial-gradient(circle_600px_at_50%_300px,rgba(99,102,241,0.08),transparent)]"})]}),(0,r.jsx)("div",{className:"container px-4 md:px-6 mx-auto max-w-5xl",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-8 text-center",children:[e.badge&&(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 border border-orange-200 dark:border-orange-700 rounded-full text-sm font-medium text-orange-800 dark:text-orange-200",children:[e.badge.icon&&(0,r.jsx)("span",{children:e.badge.icon}),(0,r.jsx)("span",{children:e.badge.text}),e.badge.icon&&(0,r.jsx)("span",{children:e.badge.icon})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"space-y-6 max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white leading-tight",children:e.title}),(0,r.jsx)("p",{className:"text-lg text-gray-600 md:text-xl lg:text-2xl dark:text-gray-300 max-w-3xl mx-auto leading-relaxed",children:e.subtitle}),(0,r.jsx)("p",{className:"text-base text-gray-500 md:text-lg dark:text-gray-400 max-w-2xl mx-auto leading-relaxed",children:e.description})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2,ease:"easeOut"},className:"flex flex-col sm:flex-row items-center justify-center gap-4",children:[(0,r.jsx)(s.$,{size:"lg",className:"min-w-[160px] bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-lg px-8 h-12 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:e.cta.primary}),(0,r.jsx)(s.$,{variant:"outline",size:"lg",className:"min-w-[160px] rounded-lg px-8 h-12 text-base font-semibold border border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800 transition-all duration-300 transform hover:scale-105",children:e.cta.secondary})]}),e.socialProof&&(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"flex flex-col items-center gap-4 mt-8",children:[(0,r.jsx)("div",{className:"flex items-center -space-x-2",children:Array.from({length:e.socialProof?.avatarCount||6},(t,a)=>{let s=["from-blue-400 to-blue-600","from-green-400 to-green-600","from-purple-400 to-purple-600","from-orange-400 to-orange-600","from-pink-400 to-pink-600","from-indigo-400 to-indigo-600","from-cyan-400 to-cyan-600","from-red-400 to-red-600"],n=a===(e.socialProof?.avatarCount||6)-1?"+":String.fromCharCode(65+a);return(0,r.jsx)("div",{className:`w-10 h-10 rounded-full bg-gradient-to-r ${s[a%s.length]} border-2 border-white dark:border-gray-800 flex items-center justify-center text-white text-sm font-semibold`,children:n},a)})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:e.socialProof.text})]})]})})]})}},30630:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var r=a(43147);a(26926);var s=a(65250),n=a(88056);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...a}){return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:t}),e),...a})}},32485:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(29100),s=a(61286),n=a(99086);let i=(0,r.A)(async({requestLocale:e})=>{let t=await e,r=(0,s.EL)(n.DT.locales,t)?t:n.DT.defaultLocale;return{locale:r,messages:(await a(76565)(`./${r}.json`)).default,timeZone:"Asia/Shanghai"}})},41502:(e,t,a)=>{"use strict";a.d(t,{J:()=>d});var r=a(43147),s=a(26926),n=a(7507),i=a(65250),o=a(88056);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.b,{ref:a,className:(0,o.cn)(l(),e),...t}));d.displayName=n.b.displayName},45443:(e,t,a)=>{"use strict";a.d(t,{Hero:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/Hero.tsx","Hero")},48315:(e,t,a)=>{"use strict";a.d(t,{FAQ:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call FAQ() from the server but FAQ is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/FAQ.tsx","FAQ")},49181:(e,t,a)=>{Promise.resolve().then(a.bind(a,8081)),Promise.resolve().then(a.bind(a,66713)),Promise.resolve().then(a.bind(a,5089)),Promise.resolve().then(a.bind(a,88697)),Promise.resolve().then(a.bind(a,3049)),Promise.resolve().then(a.bind(a,79159)),Promise.resolve().then(a.bind(a,93502)),Promise.resolve().then(a.bind(a,74774)),Promise.resolve().then(a.bind(a,28669)),Promise.resolve().then(a.bind(a,78439)),Promise.resolve().then(a.bind(a,80580)),Promise.resolve().then(a.bind(a,86300))},51871:(e,t,a)=>{"use strict";a.d(t,{Providers:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/providers.tsx","Providers")},53917:(e,t,a)=>{Promise.resolve().then(a.bind(a,14239)),Promise.resolve().then(a.bind(a,49715)),Promise.resolve().then(a.bind(a,51871)),Promise.resolve().then(a.bind(a,1453)),Promise.resolve().then(a.bind(a,83887)),Promise.resolve().then(a.bind(a,77997)),Promise.resolve().then(a.bind(a,48315)),Promise.resolve().then(a.bind(a,11846)),Promise.resolve().then(a.bind(a,45443)),Promise.resolve().then(a.bind(a,12413)),Promise.resolve().then(a.bind(a,62694)),Promise.resolve().then(a.bind(a,68382))},54155:(e,t,a)=>{"use strict";a.d(t,{dk:()=>s.Benefits,J0:()=>f.CTA,Tw:()=>g.FAQ,wi:()=>b.Footer,lq:()=>r.Hero,xw:()=>v.default,vl:()=>d,Uz:()=>n.Stats,Gm:()=>u});var r=a(45443),s=a(83887),n=a(62694),i=a(21393),o=a(1453);let l=["monthly","yearly"];function d({pricing:e}){let t=e.plans.map((t,a)=>({id:t.name.toLowerCase().replace(/\s+/g,"-"),name:t.name,price:{monthly:t.monthlyPrice,yearly:t.yearlyPrice},originalPrice:t.originalPrice?{monthly:t.originalPrice,yearly:t.originalPrice}:void 0,description:t.description,features:t.features,cta:"Get Start ⚡",popular:1===a,highlighted:a===e.plans.length-1})),a={monthly:e.frequencies?.monthly||"Monthly",yearly:e.frequencies?.yearly||"Yearly"};return(0,i.jsxs)("section",{id:"pricing",className:"py-24 relative overflow-hidden bg-background",children:[(0,i.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background to-background"}),(0,i.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_800px_at_50%_400px,rgba(59,130,246,0.03),transparent)] dark:bg-[radial-gradient(circle_800px_at_50%_400px,rgba(59,130,246,0.06),transparent)]"}),(0,i.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_600px_at_20%_200px,rgba(139,92,246,0.02),transparent)] dark:bg-[radial-gradient(circle_600px_at_20%_200px,rgba(139,92,246,0.04),transparent)]"})]}),(0,i.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,i.jsx)("div",{className:"h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(to_bottom,rgba(0,0,0,0.015)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px]"})}),(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsx)(o.PricingSection,{title:e.title,subtitle:e.subtitle,frequencies:l,frequencyLabels:a,tiers:t})})]})}var c=a(24750),m=a(68382);function x({author:e,text:t,href:a,className:r}){return(0,i.jsxs)(a?"a":"div",{...a?{href:a}:{},className:(0,c.cn)("flex flex-col rounded-lg border-t","bg-gradient-to-b from-muted/50 to-muted/10","p-4 text-start sm:p-6","hover:from-muted/60 hover:to-muted/20","max-w-[320px] sm:max-w-[320px]","transition-colors duration-300",r),children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)(m.Avatar,{className:"h-12 w-12",children:(0,i.jsx)(m.AvatarImage,{src:e.avatar,alt:e.name})}),(0,i.jsxs)("div",{className:"flex flex-col items-start",children:[(0,i.jsx)("h3",{className:"text-md font-semibold leading-none",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:e.handle})]})]}),(0,i.jsx)("p",{className:"sm:text-md mt-4 text-sm text-muted-foreground",children:t})]})}function h({title:e,description:t,testimonials:a,className:r}){return(0,i.jsxs)("section",{className:(0,c.cn)("bg-background text-foreground relative overflow-hidden","py-12 sm:py-24 md:py-32 px-0",r),children:[(0,i.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background to-background"}),(0,i.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_600px_at_50%_50%,rgba(139,92,246,0.03),transparent)] dark:bg-[radial-gradient(circle_600px_at_50%_50%,rgba(139,92,246,0.06),transparent)]"})]}),(0,i.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,i.jsx)("div",{className:"h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(to_bottom,rgba(0,0,0,0.015)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px]"})}),(0,i.jsxs)("div",{className:"mx-auto flex max-w-container flex-col items-center gap-4 text-center sm:gap-16",children:[(0,i.jsxs)("div",{className:"flex flex-col items-center gap-4 px-4 sm:gap-8",children:[(0,i.jsx)("h2",{className:"max-w-[720px] text-4xl font-bold leading-tight sm:text-5xl sm:leading-tight bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300",children:e}),(0,i.jsx)("p",{className:"text-lg max-w-[600px] font-medium text-gray-600 dark:text-gray-300 sm:text-xl leading-relaxed",children:t})]}),(0,i.jsxs)("div",{className:"relative flex w-full flex-col items-center justify-center overflow-hidden",children:[(0,i.jsx)("div",{className:"group flex overflow-hidden p-2 [--gap:1rem] [gap:var(--gap)] flex-row [--duration:40s]",children:(0,i.jsx)("div",{className:"flex shrink-0 justify-around [gap:var(--gap)] animate-marquee flex-row group-hover:[animation-play-state:paused]",children:[void 0,void 0,void 0,void 0].map((e,t)=>a.map((e,a)=>(0,i.jsx)(x,{...e},`${t}-${a}`)))})}),(0,i.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 hidden w-1/3 bg-gradient-to-r from-background to-transparent sm:block"}),(0,i.jsx)("div",{className:"pointer-events-none absolute inset-y-0 right-0 hidden w-1/3 bg-gradient-to-l from-background to-transparent sm:block"})]})]})]})}let p=[{author:{name:"Emma Thompson",handle:"@emmaai",avatar:"/avatars/01.jpeg"},text:"Using this AI platform has transformed how we handle data analysis. The speed and accuracy are unprecedented.",href:"https://twitter.com/emmaai"},{author:{name:"David Park",handle:"@davidtech",avatar:"/avatars/02.jpeg"},text:"The API integration is flawless. We've reduced our development time by 60% since implementing this solution.",href:"https://twitter.com/davidtech"},{author:{name:"Sofia Rodriguez",handle:"@sofiaml",avatar:"/avatars/03.jpeg"},text:"Finally, an AI tool that actually understands context! The accuracy in natural language processing is impressive."}];function u({section:e}){return(0,i.jsx)("section",{id:"testimonials",children:(0,i.jsx)(h,{title:e.title,description:e.subtitle,testimonials:p})})}var g=a(48315),f=a(77997),b=a(11846),v=a(12413)},56783:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var r=a(43147),s=a(26926),n=a(57618),i=a(65250),o=a(88056);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:t,size:a,asChild:s=!1,...i},d)=>{let c=s?n.DX:"button";return(0,r.jsx)(c,{className:(0,o.cn)(l({variant:t,size:a,className:e})),ref:d,...i})});d.displayName="Button"},62694:(e,t,a)=>{"use strict";a.d(t,{Stats:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call Stats() from the server but Stats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/Stats.tsx","Stats")},67060:(e,t,a)=>{"use strict";async function r(e){try{let t;try{t=(await a(76565)(`./${e}.json`)).default}catch{t=(await a.e(368).then(a.t.bind(a,87368,19))).default}return{header:t.header,footer:t.footer,hero:t.hero,benefit:t.benefit,stats:t.stats,pricing:t.pricing,testimonial:t.testimonial,faq:t.faq,cta:t.cta}}catch(e){throw e}}a.d(t,{J:()=>r})},68382:(e,t,a)=>{"use strict";a.d(t,{Avatar:()=>s,AvatarImage:()=>n});var r=a(57479);let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ui/avatar.tsx","Avatar"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ui/avatar.tsx","AvatarImage");(0,r.registerClientReference)(function(){throw Error("Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/ui/avatar.tsx","AvatarFallback")},74774:(e,t,a)=>{"use strict";a.d(t,{Footer:()=>C});var r=a(43147),s=a(26926),n=a(56783),i=a(25431),o=a(41502),l=a(15810),d=a(88056);let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(l.bL,{className:(0,d.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:a,children:(0,r.jsx)(l.zi,{className:(0,d.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));c.displayName=l.bL.displayName;var m=a(58584);let x=m.Kq,h=m.bL,p=m.l9,u=s.forwardRef(({className:e,sideOffset:t=4,...a},s)=>(0,r.jsx)(m.UC,{ref:s,sideOffset:t,className:(0,d.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));u.displayName=m.UC.displayName;var g=a(48735),f=a(56557),b=a(30414),v=a(42097),y=a(63927),j=a(2e3),N=a(69327),w=a(73075),k=a(27400),_=a(80763);function C({footer:e}){let{theme:t,setTheme:a}=(0,_.D)(),[l,d]=(0,s.useState)(!1);return e?(0,r.jsxs)("footer",{id:"contact",className:"relative border-t border-border bg-background transition-colors duration-300",children:[(0,r.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background to-background"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_600px_at_50%_50%,rgba(59,130,246,0.02),transparent)] dark:bg-[radial-gradient(circle_600px_at_50%_50%,rgba(59,130,246,0.04),transparent)]"})]}),(0,r.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,r.jsx)("div",{className:"h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.01)_1px,transparent_1px),linear-gradient(to_bottom,rgba(0,0,0,0.01)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(255,255,255,0.015)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.015)_1px,transparent_1px)] bg-[size:40px_40px]"})}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-16 md:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"grid gap-12 md:grid-cols-2 lg:grid-cols-5",children:[(0,r.jsxs)("div",{className:"relative lg:col-span-2",children:[(0,r.jsx)("h2",{className:"mb-4 text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600",children:e.newsletter.title}),(0,r.jsx)("p",{className:"mb-6 text-muted-foreground leading-relaxed",children:e.newsletter.description}),(0,r.jsxs)("form",{className:"relative mb-6",children:[(0,r.jsx)(i.p,{type:"email",placeholder:e.newsletter.placeholder,className:"pr-12 backdrop-blur-sm border-gray-200 dark:border-gray-700 rounded-full h-12"}),(0,r.jsxs)(n.$,{type:"submit",size:"icon",className:"absolute right-1 top-1 h-10 w-10 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 hover:scale-105",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:e.newsletter.subscribeAria})]})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground italic",children:e.tagline}),(0,r.jsx)("div",{className:"absolute -right-4 top-0 h-24 w-24 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 blur-2xl"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-6 text-lg font-semibold text-foreground",children:e.quickLinks.title}),(0,r.jsxs)("nav",{className:"space-y-3 text-sm",children:[(0,r.jsx)("a",{href:"#hero",className:"block transition-colors hover:text-primary font-medium",children:e.quickLinks.home}),(0,r.jsx)("a",{href:"#features",className:"block transition-colors hover:text-primary",children:e.quickLinks.features}),(0,r.jsx)("a",{href:"#pricing",className:"block transition-colors hover:text-primary",children:e.quickLinks.pricing}),(0,r.jsx)("a",{href:"/blog",className:"block transition-colors hover:text-primary",children:e.quickLinks.blog}),(0,r.jsx)("a",{href:"/about",className:"block transition-colors hover:text-primary",children:e.quickLinks.about})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-6 text-lg font-semibold text-foreground",children:e.resources.title}),(0,r.jsxs)("nav",{className:"space-y-3 text-sm",children:[(0,r.jsx)("a",{href:"/docs",className:"block transition-colors hover:text-primary",children:e.resources.documentation}),(0,r.jsx)("a",{href:"/tutorials",className:"block transition-colors hover:text-primary",children:e.resources.tutorials}),(0,r.jsx)("a",{href:"/community",className:"block transition-colors hover:text-primary",children:e.resources.community}),(0,r.jsx)("a",{href:"/support",className:"block transition-colors hover:text-primary",children:e.resources.support}),(0,r.jsx)("a",{href:"/api",className:"block transition-colors hover:text-primary",children:e.resources.api})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-6 text-lg font-semibold text-foreground",children:e.company.title}),(0,r.jsxs)("nav",{className:"space-y-3 text-sm",children:[(0,r.jsx)("a",{href:"/about",className:"block transition-colors hover:text-primary",children:e.company.about}),(0,r.jsx)("a",{href:"/careers",className:"block transition-colors hover:text-primary",children:e.company.careers}),(0,r.jsx)("a",{href:"/press",className:"block transition-colors hover:text-primary",children:e.company.press}),(0,r.jsx)("a",{href:"/contact",className:"block transition-colors hover:text-primary",children:e.company.contact}),(0,r.jsx)("a",{href:"/partners",className:"block transition-colors hover:text-primary",children:e.company.partners})]})]})]}),(0,r.jsxs)("div",{className:"mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-6 text-lg font-semibold text-foreground",children:e.social.title}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsx)(x,{children:(0,r.jsxs)(h,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"icon",className:"rounded-full hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-900/20",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Facebook"})]})}),(0,r.jsx)(u,{children:(0,r.jsx)("p",{children:e.social.facebook})})]})}),(0,r.jsx)(x,{children:(0,r.jsxs)(h,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"icon",className:"rounded-full hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-900/20",children:[(0,r.jsx)(b.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Twitter"})]})}),(0,r.jsx)(u,{children:(0,r.jsx)("p",{children:e.social.twitter})})]})}),(0,r.jsx)(x,{children:(0,r.jsxs)(h,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"icon",className:"rounded-full hover:bg-pink-50 hover:border-pink-200 dark:hover:bg-pink-900/20",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Instagram"})]})}),(0,r.jsx)(u,{children:(0,r.jsx)("p",{children:e.social.instagram})})]})}),(0,r.jsx)(x,{children:(0,r.jsxs)(h,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"icon",className:"rounded-full hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-900/20",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"LinkedIn"})]})}),(0,r.jsx)(u,{children:(0,r.jsx)("p",{children:e.social.linkedin})})]})}),(0,r.jsx)(x,{children:(0,r.jsxs)(h,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"icon",className:"rounded-full hover:bg-red-50 hover:border-red-200 dark:hover:bg-red-900/20",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"YouTube"})]})}),(0,r.jsx)(u,{children:(0,r.jsx)("p",{children:e.social.youtube})})]})}),(0,r.jsx)(x,{children:(0,r.jsxs)(h,{children:[(0,r.jsx)(p,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"icon",className:"rounded-full hover:bg-indigo-50 hover:border-indigo-200 dark:hover:bg-indigo-900/20",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Discord"})]})}),(0,r.jsx)(u,{children:(0,r.jsx)("p",{children:e.social.discord})})]})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-6 text-lg font-semibold text-foreground",children:e.contact.title}),(0,r.jsxs)("address",{className:"space-y-3 text-sm not-italic text-muted-foreground",children:[(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.contact.address]}),(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,r.jsx)("a",{href:`mailto:${e.contact.email}`,className:"hover:text-primary transition-colors",children:e.contact.email})]}),(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,r.jsx)("a",{href:`tel:${e.contact.phone}`,className:"hover:text-primary transition-colors",children:e.contact.phone})]}),(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-orange-500 rounded-full"}),e.contact.hours]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-6 text-lg font-semibold text-foreground",children:e.theme.toggle}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-full w-fit",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 text-orange-500"}),(0,r.jsx)(c,{id:"dark-mode",checked:!!l&&"dark"===t,onCheckedChange:e=>a(e?"dark":"light"),className:"data-[state=checked]:bg-blue-600"}),(0,r.jsx)(k.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)(o.J,{htmlFor:"dark-mode",className:"sr-only",children:e.theme.toggle})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:l&&"dark"===t?e.theme.dark:e.theme.light})]})]}),(0,r.jsxs)("div",{className:"mt-16 flex flex-col items-center justify-between gap-6 border-t border-gray-200 dark:border-gray-700 pt-8 md:flex-row",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground text-center md:text-left",children:e.copyright}),(0,r.jsxs)("nav",{className:"flex flex-wrap gap-6 text-sm",children:[(0,r.jsx)("a",{href:"/privacy",className:"transition-colors hover:text-primary",children:e.legal.privacy}),(0,r.jsx)("a",{href:"/terms",className:"transition-colors hover:text-primary",children:e.legal.terms}),(0,r.jsx)("a",{href:"/cookies",className:"transition-colors hover:text-primary",children:e.legal.cookies}),(0,r.jsx)("a",{href:"/dmca",className:"transition-colors hover:text-primary",children:e.legal.dmca})]})]})]})]}):null}},76565:(e,t,a)=>{var r={"./en.json":[87368,368],"./zh.json":[95342,961]};function s(e){if(!a.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],s=t[0];return a.e(t[1]).then(()=>a.t(s,19))}s.keys=()=>Object.keys(r),s.id=76565,e.exports=s},77858:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var r=a(21393),s=a(85717);function n(){return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsx)(s.Rx,{}),(0,r.jsx)(s.E0,{})]})}},77997:(e,t,a)=>{"use strict";a.d(t,{CTA:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call CTA() from the server but CTA is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/CTA.tsx","CTA")},78439:(e,t,a)=>{"use strict";a.d(t,{default:()=>W});var r=a(43147),s=a(14394),n=a.n(s),i=a(56783),o=a(45729),l=a(25937),d=a(22819),c=a(569);let m=["en","zh"],x={en:"English",zh:"简体中文"},h=(0,l.A)({locales:m,defaultLocale:"en",localePrefix:"always",localeDetection:!0}),{Link:p,redirect:u,usePathname:g,useRouter:f}=(0,d.A)(h);async function b(e){try{return(await a(18421)(`./${e}.json`)).default}catch(e){(0,o.notFound)()}}(0,c.M6)(async({locale:e})=>(e&&m.includes(e)||(0,o.notFound)(),{locale:e,messages:await b(e)}));var v=a(93096),y=a(80174),j=a(26926),N=a(8900),w=a(65250),k=a(14565),_=a(88056);let C=N.bL,P=N.l9;N.bm;let A=N.ZL,z=j.forwardRef(({className:e,...t},a)=>(0,r.jsx)(N.hJ,{className:(0,_.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:a}));z.displayName=N.hJ.displayName;let $=(0,w.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),R=j.forwardRef(({side:e="right",className:t,children:a,...s},n)=>(0,r.jsxs)(A,{children:[(0,r.jsx)(z,{}),(0,r.jsxs)(N.UC,{ref:n,className:(0,_.cn)($({side:e}),t),...s,children:[(0,r.jsxs)(N.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(k.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]}),a]})]}));R.displayName=N.UC.displayName;let S=({className:e,...t})=>(0,r.jsx)("div",{className:(0,_.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});S.displayName="SheetHeader";let L=j.forwardRef(({className:e,...t},a)=>(0,r.jsx)(N.hE,{ref:a,className:(0,_.cn)("text-lg font-semibold text-foreground",e),...t}));L.displayName=N.hE.displayName,j.forwardRef(({className:e,...t},a)=>(0,r.jsx)(N.VY,{ref:a,className:(0,_.cn)("text-sm text-muted-foreground",e),...t})).displayName=N.VY.displayName;var U=a(40519),F=a(66142),I=a(21758),E=a(91209),T=a(18169),q=a(53665);let O=I.bL,D=I.l9;I.YJ,I.ZL,I.Pb,I.z6,j.forwardRef(({className:e,inset:t,children:a,...s},n)=>(0,r.jsxs)(I.ZP,{ref:n,className:(0,_.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...s,children:[a,(0,r.jsx)(E.A,{className:"ml-auto"})]})).displayName=I.ZP.displayName,j.forwardRef(({className:e,...t},a)=>(0,r.jsx)(I.G5,{ref:a,className:(0,_.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=I.G5.displayName;let H=j.forwardRef(({className:e,sideOffset:t=4,...a},s)=>(0,r.jsx)(I.ZL,{children:(0,r.jsx)(I.UC,{ref:s,sideOffset:t,className:(0,_.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})}));H.displayName=I.UC.displayName;let V=j.forwardRef(({className:e,inset:t,...a},s)=>(0,r.jsx)(I.q7,{ref:s,className:(0,_.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",t&&"pl-8",e),...a}));V.displayName=I.q7.displayName,j.forwardRef(({className:e,children:t,checked:a,...s},n)=>(0,r.jsxs)(I.H_,{ref:n,className:(0,_.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:a,...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(I.VF,{children:(0,r.jsx)(T.A,{className:"h-4 w-4"})})}),t]})).displayName=I.H_.displayName,j.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(I.hN,{ref:s,className:(0,_.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(I.VF,{children:(0,r.jsx)(q.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=I.hN.displayName,j.forwardRef(({className:e,inset:t,...a},s)=>(0,r.jsx)(I.JU,{ref:s,className:(0,_.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...a})).displayName=I.JU.displayName,j.forwardRef(({className:e,...t},a)=>(0,r.jsx)(I.wv,{ref:a,className:(0,_.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=I.wv.displayName;var B=a(73075),J=a(27400),M=a(80763);function Q(){let{setTheme:e,theme:t}=(0,M.D)(),[a,s]=(0,j.useState)(!1);return a?(0,r.jsxs)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9 relative",onClick:()=>e("light"===t?"dark":"light"),"aria-label":`Switch to ${"light"===t?"dark":"light"} theme`,title:`Switch to ${"light"===t?"dark":"light"} theme`,children:[(0,r.jsx)(B.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,r.jsx)(J.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"切换主题"})]}):(0,r.jsxs)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9 relative",disabled:!0,"aria-label":"Loading theme toggle",children:[(0,r.jsx)(B.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,r.jsx)("span",{className:"sr-only",children:"切换主题"})]})}function W({header:e}){let{data:t}=(0,U.useSession)(),a=(0,o.usePathname)(),s=a.split("/")[1],[l,d]=(0,j.useState)(!1),c=(0,j.useRef)(null),h=e=>{let t=a.replace(`/${s}`,`/${e}`);window.location.href=t};return(0,r.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-gray-200/50 dark:border-gray-800/50 bg-white/80 dark:bg-background/80 backdrop-blur-md supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-background/60 transition-colors duration-300",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 w-full",children:(0,r.jsxs)("nav",{className:"relative flex h-16 items-center justify-between",children:[(0,r.jsx)("div",{className:"flex-none",children:(0,r.jsxs)(n(),{href:"/",className:"flex items-center group",children:[(0,r.jsx)(F.default,{src:"/logo.png",alt:"Logo",width:64,height:64,className:"h-8 w-8"}),(0,r.jsx)("span",{className:"text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 group-hover:from-blue-700 group-hover:to-purple-700 transition-all duration-300",children:e.logo})]})}),(0,r.jsx)("div",{className:"hidden md:flex items-center justify-center flex-1 px-8",children:(0,r.jsx)("div",{className:"flex space-x-8",children:Object.entries(e.nav).map(([e,t])=>{let s={benefit:"features",stats:"stats",pricing:"pricing",testimonial:"testimonials",faq:"faq"}[e]||e,i=a.includes(`#${s}`)||a.endsWith(`#${s}`);return(0,r.jsx)(n(),{href:`/${a.split("/")[1]}#${s}`,className:`text-sm hover:text-gray-900 dark:hover:text-white transition-colors duration-300 ${i?"text-primary font-medium":"text-gray-600 dark:text-gray-300"}`,children:t},e)})})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-6",children:[(0,r.jsx)(Q,{}),(0,r.jsxs)(O,{children:[(0,r.jsxs)(D,{className:"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-300",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:x[s]})]}),(0,r.jsx)(H,{children:m.map(e=>(0,r.jsx)(V,{onClick:()=>h(e),className:"cursor-pointer",children:x[e]},e))})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:t?(0,r.jsxs)("div",{className:"relative",ref:c,children:[(0,r.jsx)("div",{children:(0,r.jsxs)("button",{type:"button",className:"flex items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",onClick:()=>d(!l),children:[(0,r.jsx)("span",{className:"sr-only",children:"Open user menu"}),t.user?.image?(0,r.jsx)(F.default,{className:"h-8 w-8 rounded-full",src:t.user.image,alt:t.user.name||"",width:32,height:32,unoptimized:!0,priority:!0}):(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-600",children:t.user?.name?.charAt(0)||"?"})})]})}),l&&(0,r.jsxs)("div",{className:"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm py-1 shadow-lg ring-1 ring-black/5 dark:ring-white/10 border border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("div",{className:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300",children:(0,r.jsx)("div",{className:"font-medium",children:t.user?.name})}),(0,r.jsx)("div",{className:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300",children:(0,r.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:t.user?.email})}),(0,r.jsx)("div",{className:"border-t border-gray-100 dark:border-gray-700"}),(0,r.jsx)(n(),{href:`/${s}/orders`,className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors",children:e.userMenu.myOrders}),(0,r.jsx)(n(),{href:`/${s}/profile`,className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors",children:e.userMenu.profile}),(0,r.jsx)("button",{type:"button",onClick:()=>(0,U.signOut)({callbackUrl:`/${s}`}),className:"block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors",children:e.userMenu.signOut})]})]}):(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(i.$,{onClick:()=>(0,U.signIn)(),size:"sm",className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full px-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:e.cta.login})})})]}),(0,r.jsxs)("div",{className:"flex md:hidden",children:[(0,r.jsx)(Q,{}),(0,r.jsxs)(C,{children:[(0,r.jsx)(P,{asChild:!0,children:(0,r.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9",children:(0,r.jsx)(y.A,{className:"h-5 w-5"})})}),(0,r.jsxs)(R,{side:"right",children:[(0,r.jsx)(S,{children:(0,r.jsx)(L,{children:e.logo})}),(0,r.jsxs)("div",{className:"flex flex-col space-y-4 mt-6",children:[Object.entries(e.nav).map(([e,t])=>{let s={benefit:"features",stats:"stats",pricing:"pricing",testimonial:"testimonials",faq:"faq"}[e]||e,i=a.includes(`#${s}`)||a.endsWith(`#${s}`);return(0,r.jsx)(n(),{href:`/${a.split("/")[1]}#${s}`,className:`text-sm hover:text-gray-900 transition-colors ${i?"text-primary font-medium":"text-gray-600"}`,children:t},e)}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600",onClick:()=>h(s),children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:x[s]})]}),t?(0,r.jsxs)("div",{className:"flex flex-col space-y-3 pt-4",children:[(0,r.jsx)("div",{className:"font-medium",children:t.user?.name}),(0,r.jsx)("div",{className:"text-gray-500",children:t.user?.email}),(0,r.jsx)(n(),{href:`/${s}/orders`,className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors rounded-md",children:e.userMenu.myOrders}),(0,r.jsx)(n(),{href:`/${s}/profile`,className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors rounded-md",children:e.userMenu.profile}),(0,r.jsx)("button",{type:"button",onClick:()=>(0,U.signOut)({callbackUrl:`/${s}`}),className:"block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors rounded-md",children:e.userMenu.signOut})]}):(0,r.jsx)("div",{className:"flex flex-col space-y-3 pt-4",children:(0,r.jsx)(i.$,{onClick:()=>(0,U.signIn)(),size:"sm",className:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full px-6 shadow-lg hover:shadow-xl transition-all duration-300",children:e.cta.login})})]})]})]})]})]})})})}},79159:(e,t,a)=>{"use strict";a.d(t,{CTA:()=>i});var r=a(43147),s=a(56783),n=a(62948);function i({section:e}){return(0,r.jsxs)("section",{id:"cta",className:"py-24 relative overflow-hidden bg-background",children:[(0,r.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background to-background"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_800px_at_50%_400px,rgba(59,130,246,0.03),transparent)] dark:bg-[radial-gradient(circle_800px_at_50%_400px,rgba(59,130,246,0.06),transparent)]"})]}),(0,r.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,r.jsx)("div",{className:"h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(to_bottom,rgba(0,0,0,0.015)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px]"})}),(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300",children:e.title}),(0,r.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-10",children:e.subtitle}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,r.jsx)(s.$,{size:"lg",className:"bg-primary hover:bg-primary/90 text-white rounded-full px-8 h-12 text-base",children:e.cta.primary}),(0,r.jsx)(s.$,{size:"lg",variant:"outline",className:"rounded-full px-8 h-12 text-base border-gray-300 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-gray-800",children:e.cta.secondary})]})]})})]})}},80580:(e,t,a)=>{"use strict";a.d(t,{Stats:()=>n});var r=a(43147),s=a(62948);function n({section:e}){return(0,r.jsxs)("section",{id:"stats",className:"py-24 relative overflow-hidden bg-background",children:[(0,r.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background to-background"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_800px_at_50%_200px,rgba(6,182,212,0.03),transparent)] dark:bg-[radial-gradient(circle_800px_at_50%_200px,rgba(6,182,212,0.06),transparent)]"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_600px_at_80%_400px,rgba(139,92,246,0.02),transparent)] dark:bg-[radial-gradient(circle_600px_at_80%_400px,rgba(139,92,246,0.04),transparent)]"})]}),(0,r.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,r.jsx)("div",{className:"h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(to_bottom,rgba(0,0,0,0.015)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px]"})}),(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300",children:e.title}),(0,r.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed",children:e.subtitle})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:e.stats.map((e,t)=>(0,r.jsx)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"group text-center",children:(0,r.jsxs)("div",{className:"relative bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600 mb-4 group-hover:scale-110 transition-transform duration-300",children:e.value}),(0,r.jsx)("div",{className:"text-lg font-semibold mb-3 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:e.label}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 leading-relaxed",children:e.description})]}),(0,r.jsx)("div",{className:"absolute top-4 right-4 w-2 h-2 bg-blue-400/30 rounded-full group-hover:bg-blue-400/60 transition-colors"}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 w-1 h-1 bg-cyan-400/30 rounded-full group-hover:bg-cyan-400/60 transition-colors"})]})},t))})]})]})}},83887:(e,t,a)=>{"use strict";a.d(t,{Benefits:()=>r});let r=(0,a(57479).registerClientReference)(function(){throw Error("Attempted to call Benefits() from the server but Benefits is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/sections/Benefits.tsx","Benefits")},85717:(e,t,a)=>{"use strict";a.d(t,{E0:()=>o,EA:()=>n,Rx:()=>i});var r=a(21393),s=a(24750);function n({className:e,...t}){return(0,r.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",e),...t})}function i(){return(0,r.jsx)("div",{className:"w-full h-16 border-b",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 h-full flex items-center justify-between",children:[(0,r.jsx)(n,{className:"h-8 w-32"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(n,{className:"h-8 w-20"}),(0,r.jsx)(n,{className:"h-8 w-20"}),(0,r.jsx)(n,{className:"h-8 w-8 rounded-full"})]})]})})}function o(){return(0,r.jsx)("div",{className:"container mx-auto px-4 py-20",children:(0,r.jsxs)("div",{className:"text-center space-y-6",children:[(0,r.jsx)(n,{className:"h-12 w-3/4 mx-auto"}),(0,r.jsx)(n,{className:"h-6 w-2/3 mx-auto"}),(0,r.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,r.jsx)(n,{className:"h-10 w-32"}),(0,r.jsx)(n,{className:"h-10 w-32"})]})]})})}},86300:(e,t,a)=>{"use strict";a.d(t,{Avatar:()=>o,AvatarImage:()=>l});var r=a(43147),s=a(26926),n=a(70294),i=a(88056);let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.bL,{ref:a,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));o.displayName=n.bL.displayName;let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n._V,{ref:a,className:(0,i.cn)("aspect-square h-full w-full",e),...t}));l.displayName=n._V.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.H4,{ref:a,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t})).displayName=n.H4.displayName},88056:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(1786),s=a(28587);function n(...e){return(0,s.QP)((0,r.$)(e))}},88697:(e,t,a)=>{"use strict";a.d(t,{PricingSection:()=>g});var r=a(43147),s=a(26926),n=a(91251),i=a(5495),o=a(40519),l=a(45729),d=a(96313),c=a(67880),m=a(88056),x=a(30630),h=a(56783);let p=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,m.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));function u({tier:e,paymentFrequency:t}){let a=e.price[t],s=e.originalPrice?.[t],u=e.highlighted,g=e.popular,{data:f}=(0,o.useSession)(),b=(0,l.useRouter)(),v=(0,l.usePathname)(),y=v.split("/")[1]||"",j=(0,c.c3)("pricing"),N=async()=>{if("Contact Us"===e.cta)return void b.push("/contact");if("number"!=typeof a)return"Custom"===a?void b.push("/contact"):"Free"===a?void d.oR.success("zh"===y?"已选择免费方案":"Free plan selected"):void 0;if(!f){d.oR.error(j("pleaseLogin")),b.push(`/auth/signin?callbackUrl=${encodeURIComponent(v)}`);return}try{let t=await fetch("/api/stripe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({price:a,email:f.user?.email,productName:`${e.name} - ShipSaas`,successUrl:`${window.location.origin}/${y}/orders?session_id={CHECKOUT_SESSION_ID}&amount=${a}`,cancelUrl:`${window.location.origin}/${y}/#pricing`})});if(!t.ok){let e=await t.json();throw Error(e.error||"zh"===y?"支付请求失败":"Payment request failed")}let{url:r}=await t.json();if(r)window.location.href=r;else throw Error("zh"===y?"未收到结账 URL":"No checkout URL received")}catch(e){d.oR.error(e instanceof Error?e.message:"zh"===y?"支付失败，请重试":"Payment failed. Please try again.")}};return(0,r.jsxs)("div",{className:(0,m.cn)("relative w-full max-w-sm",g&&"pt-4"),children:[g&&(0,r.jsx)("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 z-30",children:(0,r.jsx)(x.E,{className:"bg-orange-500 text-white px-4 py-1.5 text-sm font-medium rounded-full shadow-lg whitespace-nowrap border-2 border-white",children:"zh"===y?"最受欢迎":"Popular"})}),(0,r.jsxs)(p,{className:(0,m.cn)("relative flex flex-col gap-6 p-8 transition-all duration-300 hover:shadow-lg group w-full rounded-lg",u?"bg-white dark:bg-gray-900 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 shadow-md":"bg-white dark:bg-gray-900 text-foreground border border-gray-200 dark:border-gray-700 shadow-sm",g&&"border-2 border-orange-400 mt-4"),children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:e.name})}),(0,r.jsx)("div",{className:"text-center mb-6",children:"number"==typeof a?(0,r.jsxs)("div",{className:"space-y-2",children:[s&&"number"==typeof s&&(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400 line-through",children:["$",s," USD"]}),(0,r.jsxs)("div",{className:"flex items-baseline justify-center gap-1",children:[(0,r.jsx)(i.Ay,{format:{style:"currency",currency:"USD",trailingZeroDisplay:"stripIfInteger"},value:a,className:"text-5xl font-bold text-gray-900 dark:text-white"}),(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 ml-1",children:"USD"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"zh"===y?"一次性付费。无限制构建项目！":"Pay once. Build unlimited projects!"})]}):(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white",children:a})}),(0,r.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,r.jsx)("h3",{className:"text-center text-gray-600 dark:text-gray-400 text-sm",children:e.description}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-3",children:"zh"===y?"包含":"Includes"})}),(0,r.jsx)("ul",{className:"space-y-3",children:e.features.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start gap-3 text-sm text-gray-700 dark:text-gray-300",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mt-0.5 text-green-500 dark:text-green-400 flex-shrink-0"}),(0,r.jsx)("span",{children:e})]},t))})]}),(0,r.jsxs)(h.$,{className:"w-full py-3 text-base font-semibold transition-all duration-300 bg-orange-500 hover:bg-orange-600 text-white border-0",onClick:N,children:[e.cta," ⚡"]}),(0,r.jsx)("div",{className:"text-center text-xs text-gray-500 dark:text-gray-400 mt-2",children:"zh"===y?"一次性付费。无限制构建项目！":"Pay once. Build unlimited projects!"})]})]})}function g({title:e,subtitle:t,tiers:a}){return(0,r.jsxs)("section",{className:"flex flex-col items-center gap-16 py-20",children:[(0,r.jsxs)("div",{className:"space-y-6 text-center max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-5xl font-bold text-gray-900 dark:text-white",children:e}),(0,r.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:t})]}),(0,r.jsx)("div",{className:"grid w-full max-w-6xl gap-8 md:grid-cols-3 justify-items-center px-4 pt-8",children:a.map(e=>(0,r.jsx)(u,{tier:e,paymentFrequency:"monthly"},e.name))})]})}p.displayName="Card",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,m.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("h3",{ref:a,className:(0,m.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("p",{ref:a,className:(0,m.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,m.cn)("p-6 pt-0",e),...t})).displayName="CardContent",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,m.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},93502:(e,t,a)=>{"use strict";a.d(t,{FAQ:()=>x});var r=a(43147),s=a(26926),n=a(51381),i=a(44763),o=a(88056);let l=n.bL,d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.q7,{ref:a,className:(0,o.cn)("border-b",e),...t}));d.displayName="AccordionItem";let c=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsx)(n.Y9,{className:"flex",children:(0,r.jsxs)(n.l9,{ref:s,className:(0,o.cn)("flex flex-1 items-center justify-between py-4 text-sm font-medium transition-all hover:underline text-left [&[data-state=open]>svg]:rotate-180",e),...a,children:[t,(0,r.jsx)(i.A,{className:"h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200"})]})}));c.displayName=n.l9.displayName;let m=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsx)(n.UC,{ref:s,className:"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...a,children:(0,r.jsx)("div",{className:(0,o.cn)("pb-4 pt-0",e),children:t})}));function x({section:e}){return(0,r.jsxs)("section",{id:"faq",className:"py-24 bg-background relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-background to-background"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_600px_at_50%_50%,rgba(6,182,212,0.03),transparent)] dark:bg-[radial-gradient(circle_600px_at_50%_50%,rgba(6,182,212,0.06),transparent)]"})]}),(0,r.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,r.jsx)("div",{className:"h-full w-full bg-[linear-gradient(to_right,rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(to_bottom,rgba(0,0,0,0.015)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px]"})}),(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300",children:e.title}),(0,r.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed",children:e.subtitle})]}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsx)("div",{className:"bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-200 dark:border-gray-700 shadow-xl p-8",children:(0,r.jsx)(l,{type:"single",collapsible:!0,className:"space-y-4",children:e.faqs.map((e,t)=>(0,r.jsxs)(d,{value:`item-${t}`,className:"border border-gray-200 dark:border-gray-700 rounded-xl px-6 py-2 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-gray-900/80 transition-all duration-300",children:[(0,r.jsx)(c,{className:"text-left text-gray-900 dark:text-white font-semibold hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:e.question}),(0,r.jsx)(m,{className:"text-gray-600 dark:text-gray-300 leading-relaxed pt-2",children:e.answer})]},t))})})})]})]})}m.displayName=n.UC.displayName},99086:(e,t,a)=>{"use strict";a.d(t,{DT:()=>l,VL:()=>h});var r=a(82651),s=a(75133),n=a(29100),i=a(64064);let o=["en","zh"],l=(0,r.A)({locales:o,defaultLocale:"en",localePrefix:"always",localeDetection:!0}),{Link:d,redirect:c,usePathname:m,useRouter:x}=(0,s.A)(l);async function h(e){try{return(await a(76565)(`./${e}.json`)).default}catch(e){(0,i.notFound)()}}(0,n.A)(async({locale:e})=>(e&&o.includes(e)||(0,i.notFound)(),{locale:e,messages:await h(e)}))}};