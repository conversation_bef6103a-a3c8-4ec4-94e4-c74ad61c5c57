{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon|.*\\..*|robots\\.txt|sitemap\\.xml|manifest\\.json).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon|.*\\..*|robots\\.txt|sitemap\\.xml|manifest\\.json).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "MVxtIqnVSLH5ZB7VfVV84", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/izUkgplxWBWfQBES3qXBSggXNSAbPS6RX/8iemlco4=", "__NEXT_PREVIEW_MODE_ID": "0ae2508e560fff2296b490623bb4f688", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "06193a3f0237e5537cd11ec758807f118bdb5494d198c6f223a638f4fc3b126b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e095bb694e66efb73663a4bd65f6500cec46255fb851d4efa0370dd3dfca88c7"}}}, "functions": {}, "sortedMiddleware": ["/"]}