{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon|.*\\..*|robots\\.txt|sitemap\\.xml|manifest\\.json).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon|.*\\..*|robots\\.txt|sitemap\\.xml|manifest\\.json).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "fxHrLFl77V256rk_XJYsj", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/izUkgplxWBWfQBES3qXBSggXNSAbPS6RX/8iemlco4=", "__NEXT_PREVIEW_MODE_ID": "4ecc3e644c52bf406fd1e5c6e0cf9a3f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "59a74f87040c8cd3c4c18dfaac2974b43fd99d9aa8b9f8118f87b4222898278c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf8df8633fdbcf2a8c51be90de45f648d2bdd67e1e3ec7ff5da42daff91714dd"}}}, "functions": {}, "sortedMiddleware": ["/"]}