{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon|.*\\..*|robots\\.txt|sitemap\\.xml|manifest\\.json).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon|.*\\..*|robots\\.txt|sitemap\\.xml|manifest\\.json).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "7-6uNB1ybMvQxKKPPRxSu", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/izUkgplxWBWfQBES3qXBSggXNSAbPS6RX/8iemlco4=", "__NEXT_PREVIEW_MODE_ID": "65e019feaae9f9c2d06893ac2eb4c9e3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5e84a2817f5f62be70f9e5be31263f769770cd151a15958582a1d3641a33c624", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "13b5cb7405d82a48972ca36cbb656ae711021a212d30f0880572d4c841d28295"}}}, "functions": {}, "sortedMiddleware": ["/"]}