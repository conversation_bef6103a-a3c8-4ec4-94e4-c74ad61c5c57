(()=>{var e={};e.id=784,e.ids=[784],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveManifest:function(){return a},resolveRobots:function(){return i},resolveRouteData:function(){return l},resolveSitemap:function(){return n}});let o=r(83873);function i(e){let t="";for(let r of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,o.resolveArray)(r.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(r.allow)for(let e of(0,o.resolveArray)(r.allow))t+=`Allow: ${e}
`;if(r.disallow)for(let e of(0,o.resolveArray)(r.disallow))t+=`Disallow: ${e}
`;r.crawlDelay&&(t+=`Crawl-delay: ${r.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,o.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function n(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),r=e.some(e=>{var t;return!!(null==(t=e.images)?void 0:t.length)}),o=e.some(e=>{var t;return!!(null==(t=e.videos)?void 0:t.length)}),i="";for(let s of(i+='<?xml version="1.0" encoding="UTF-8"?>\n',i+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',r&&(i+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),o&&(i+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),t?i+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':i+=">\n",e)){var n,a,l;i+="<url>\n",i+=`<loc>${s.url}</loc>
`;let e=null==(n=s.alternates)?void 0:n.languages;if(e&&Object.keys(e).length)for(let t in e)i+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(null==(a=s.images)?void 0:a.length)for(let e of s.images)i+=`<image:image>
<image:loc>${e}</image:loc>
</image:image>
`;if(null==(l=s.videos)?void 0:l.length)for(let e of s.videos)i+=["<video:video>",`<video:title>${e.title}</video:title>`,`<video:thumbnail_loc>${e.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${e.description}</video:description>`,e.content_loc&&`<video:content_loc>${e.content_loc}</video:content_loc>`,e.player_loc&&`<video:player_loc>${e.player_loc}</video:player_loc>`,e.duration&&`<video:duration>${e.duration}</video:duration>`,e.view_count&&`<video:view_count>${e.view_count}</video:view_count>`,e.tag&&`<video:tag>${e.tag}</video:tag>`,e.rating&&`<video:rating>${e.rating}</video:rating>`,e.expiration_date&&`<video:expiration_date>${e.expiration_date}</video:expiration_date>`,e.publication_date&&`<video:publication_date>${e.publication_date}</video:publication_date>`,e.family_friendly&&`<video:family_friendly>${e.family_friendly}</video:family_friendly>`,e.requires_subscription&&`<video:requires_subscription>${e.requires_subscription}</video:requires_subscription>`,e.live&&`<video:live>${e.live}</video:live>`,e.restriction&&`<video:restriction relationship="${e.restriction.relationship}">${e.restriction.content}</video:restriction>`,e.platform&&`<video:platform relationship="${e.platform.relationship}">${e.platform.content}</video:platform>`,e.uploader&&`<video:uploader${e.uploader.info&&` info="${e.uploader.info}"`}>${e.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(s.lastModified){let e=s.lastModified instanceof Date?s.lastModified.toISOString():s.lastModified;i+=`<lastmod>${e}</lastmod>
`}s.changeFrequency&&(i+=`<changefreq>${s.changeFrequency}</changefreq>
`),"number"==typeof s.priority&&(i+=`<priority>${s.priority}</priority>
`),i+="</url>\n"}return i+"</urlset>\n"}function a(e){return JSON.stringify(e)}function l(e,t){return"robots"===t?i(e):"sitemap"===t?n(e):"manifest"===t?a(e):""}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},81092:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var o={};r.r(o),r.d(o,{GET:()=>u});var i=r(36331),n=r(62420),a=r(88267),l=r(91930),s=r(7163);async function u(){let e=await function(){let e=process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000";return{rules:[{userAgent:"*",allow:"/",disallow:["/api/","/admin/","/_next/","/private/","*.json"]},{userAgent:"GPTBot",disallow:"/"},{userAgent:"ChatGPT-User",disallow:"/"}],sitemap:`${e}/sitemap.xml`,host:e}}(),t=(0,s.resolveRouteData)(e,"robots");return new l.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let d=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?filePath=%2FUsers%2Fwenhao%2Ffuwenhao%2Fgithub%2Fshipsaas-office%2Fsrc%2Fapp%2Frobots.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:v}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}},83873:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e:[e]}function o(e){if(null!=e)return r(e)}function i(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getOrigin:function(){return i},resolveArray:function(){return r},resolveAsArrayOrUndefined:function(){return o}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[883,248],()=>r(81092));module.exports=o})();