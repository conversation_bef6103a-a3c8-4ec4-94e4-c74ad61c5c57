(()=>{var e={};e.id=887,e.ids=[887],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16452:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var t={};r.r(t),r.d(t,{POST:()=>p});var a=r(36331),i=r(62420),n=r(88267),u=r(91930),o=r(97641),c=r(32967),d=r(95842);async function p(e){try{let{email:s,password:r}=await e.json();if(!s||!r)return u.NextResponse.json({success:!1,message:"Email and password are required"},{status:400});if(r.length<8)return u.NextResponse.json({success:!1,message:"Password must be at least 8 characters long"},{status:400});if(await d.zR.user.findFirst({where:{email:s,signinProvider:"credentials",isDeleted:!1}}))return u.NextResponse.json({success:!1,message:"User with this email already exists"},{status:409});let t=await (0,o.tW)(r,12),a=await (0,d.Jn)({uuid:(0,c.A)(),email:s,password:t,signinProvider:"credentials",nickname:s.split("@")[0]});return u.NextResponse.json({success:!0,message:"User created successfully",userId:a.id,email:a.email},{status:201})}catch(r){let e=r instanceof Error?r.message:"Internal server error",s=e.includes("Prisma");return u.NextResponse.json({success:!1,message:s?"Database error occurred":e},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/auth/signup/route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:x}=l;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},95842:(e,s,r)=>{"use strict";r.d(s,{Jn:()=>n,iA:()=>i,zR:()=>a});let t=require("@prisma/client"),a=globalThis.prisma??new t.PrismaClient;async function i(e,s){return await a.user.findFirst({where:{email:e,signinProvider:s,isDeleted:!1}})}async function n(e){return await a.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[883,248,47],()=>r(16452));module.exports=t})();