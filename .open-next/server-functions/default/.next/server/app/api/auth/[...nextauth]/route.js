(()=>{var e={};e.id=14,e.ids=[14],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},36331:(e,r,t)=>{"use strict";e.exports=t(44870)},38629:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var a=t(65064);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s(r);if(t&&t.has(e))return t.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var u=a?Object.getOwnPropertyDescriptor(e,n):null;u&&(u.get||u.set)?Object.defineProperty(i,n,u):i[n]=e[n]}return i.default=e,t&&t.set(e,i),i}(t(35003));function s(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(s=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55102:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var i={};t.r(i),t.d(i,{GET:()=>l,POST:()=>l});var a=t(36331),n=t(62420),s=t(88267),u=t(38629),o=t.n(u),c=t(70721);let l=o()(c.$),p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/auth/[...nextauth]/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:f}=p;function g(){return(0,s.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65064:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},70721:(e,r,t)=>{"use strict";t.d(r,{$:()=>m});var i=t(83761),a=t(53829),n=t(95842),s=t(32967),u=t(20331),o=t(75758),c=t(17773),l=t(97641);t(35003);let p=(0,o.A)({id:"google-one-tap",name:"Google One Tap",credentials:{credential:{type:"text"}},async authorize(e){try{if(!e?.credential)return null;let r=(0,c.s)(e.credential),t={uuid:r.sub,email:r.email,nickname:r.name,avatarUrl:r.picture,signinType:"oauth",signinIp:"127.0.0.1",signinProvider:"google-one-tap",signinOpenid:r.sub,createdAt:new Date},i=await n.zR.user.findFirst({where:{email:r.email,signinProvider:"google-one-tap"}});return i?await n.zR.user.update({where:{id:i.id},data:{nickname:t.nickname,avatarUrl:t.avatarUrl}}):await n.zR.user.create({data:t}),{id:r.sub,email:r.email,name:r.name,image:r.picture,uuid:r.sub}}catch(e){return null}}}),d=(0,o.A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await (0,n.iA)(e.email,"credentials");return r&&r.password&&await (0,l.UD)(e.password,r.password)?{id:r.id.toString(),email:r.email,name:r.nickname||"",image:r.avatarUrl||"",uuid:r.uuid}:null}}),m={providers:[...process.env.AUTH_GITHUB_ID&&process.env.AUTH_GITHUB_SECRET?[(0,a.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET})]:[],...process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET?[(0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET})]:[],p,d],debug:!1,secret:process.env.AUTH_SECRET,cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},callbackUrl:{name:"next-auth.callback-url",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},csrfToken:{name:"next-auth.csrf-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},pkceCodeVerifier:{name:"next-auth.pkce.code_verifier",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}},state:{name:"next-auth.state",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}}},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.id=r.id,"uuid"in r&&(e.uuid=r.uuid)),r&&t&&"credentials"!==t.provider)try{let i=await n.zR.user.findFirst({where:{email:r.email||"",signinProvider:t.provider}});i||(i=await n.zR.user.create({data:{uuid:(0,s.A)(),email:r.email||"",nickname:r.name||"",avatarUrl:r.image||"",signinProvider:t.provider,signinOpenid:t.providerAccountId}})),e.id=i.id.toString(),e.uuid=i.uuid}catch(e){}return e},session:async({session:e,token:r})=>(e.user&&r&&(e.user.id=r.id,e.user.uuid=r.uuid),e),async signIn({user:e,account:r,profile:t}){try{if(!e.email)return!1;let i="127.0.0.1";try{let e=(await (0,u.headers)()).get("x-forwarded-for");i=e?e.split(",")[0]:"127.0.0.1"}catch(e){}let a={uuid:(0,s.A)(),email:e.email,nickname:t?.name||e.name,avatarUrl:t?.picture||t?.avatar_url||e.image,signinType:"oauth",signinIp:i,signinProvider:r?.provider,signinOpenid:r?.providerAccountId,createdAt:new Date},o=await n.zR.user.findFirst({where:{email:e.email,signinProvider:r?.provider}});return o?await n.zR.user.update({where:{id:o.id},data:{nickname:a.nickname,avatarUrl:a.avatarUrl,signinIp:i}}):await n.zR.user.create({data:a}),!0}catch(e){return e instanceof Error,!0}}},pages:{signIn:"/auth/signin",error:"/auth/signin"},session:{strategy:"jwt",maxAge:2592e3}}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95842:(e,r,t)=>{"use strict";t.d(r,{Jn:()=>s,iA:()=>n,zR:()=>a});let i=require("@prisma/client"),a=globalThis.prisma??new i.PrismaClient;async function n(e,r){return await a.user.findFirst({where:{email:e,signinProvider:r,isDeleted:!1}})}async function s(e){return await a.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[883,331,47,690],()=>t(55102));module.exports=i})();