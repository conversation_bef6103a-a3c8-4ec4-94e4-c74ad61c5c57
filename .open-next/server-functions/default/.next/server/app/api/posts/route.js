(()=>{var e={};e.id=988,e.ids=[988],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47579:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>w,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var a=t(36331),n=t(62420),i=t(88267),o=t(91930),u=t(95842);async function p(){try{let e=await u.zR.order.findMany();return o.NextResponse.json(e)}catch(e){return o.NextResponse.json({error:"Failed to fetch orders"},{status:500})}}async function d(e){try{let r=await e.json(),t=await u.zR.order.create({data:r});return o.NextResponse.json(t)}catch(e){return o.NextResponse.json({error:"Failed to create order"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/posts/route",pathname:"/api/posts",filename:"route",bundlePath:"app/api/posts/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/posts/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:w}=c;function h(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},95842:(e,r,t)=>{"use strict";t.d(r,{Jn:()=>i,iA:()=>n,zR:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient;async function n(e,r){return await a.user.findFirst({where:{email:e,signinProvider:r,isDeleted:!1}})}async function i(e){return await a.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[883,248],()=>t(47579));module.exports=s})();