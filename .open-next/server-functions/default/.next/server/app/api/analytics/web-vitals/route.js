(()=>{var e={};e.id=11,e.ids=[11],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51494:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>v,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var a=r(36331),n=r(62420),i=r(88267),o=r(91930);async function u(e){try{let t=await e.json();if(!t.name||"number"!=typeof t.value)return o.NextResponse.json({error:"Invalid metric data"},{status:400});return o.NextResponse.json({success:!0})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(){return o.NextResponse.json({message:"Web Vitals analytics endpoint",endpoints:{POST:"Submit web vital metrics",GET:"Retrieve aggregated metrics (not implemented)"}})}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/analytics/web-vitals/route",pathname:"/api/analytics/web-vitals",filename:"route",bundlePath:"app/api/analytics/web-vitals/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/analytics/web-vitals/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:v}=c;function x(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[883,248],()=>r(51494));module.exports=s})();