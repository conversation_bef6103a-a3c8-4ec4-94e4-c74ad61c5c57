(()=>{var e={};e.id=789,e.ids=[789],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},37867:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{GET:()=>l,POST:()=>p});var i=t(36331),s=t(62420),n=t(88267),u=t(91930),o=t(95842),d=t(38629),c=t(70721);async function l(){try{let e=await (0,d.getServerSession)(c.$);if(!e?.user?.email)return u.NextResponse.json({error:"Unauthorized"},{status:401});let r=await o.zR.user.findFirst({where:{email:e.user.email}});if(!r)return u.NextResponse.json({error:"User not found"},{status:404});let t=await o.zR.order.findMany({where:{userUuid:r.uuid},orderBy:{createdAt:"desc"}});return u.NextResponse.json(t)}catch(e){return u.NextResponse.json({error:"Failed to fetch orders"},{status:500})}}async function p(e){try{let r=await (0,d.getServerSession)(c.$);if(!r?.user?.email)return u.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),a=await o.zR.order.create({data:{orderNo:t.orderNo,userUuid:t.userUuid,userEmail:t.userEmail,amount:t.amount,interval:t.interval,status:t.status,credits:t.credits,currency:t.currency,productId:t.productId,productName:t.productName,validMonths:t.validMonths,orderDetail:t.orderDetail}});return u.NextResponse.json(a)}catch(e){return u.NextResponse.json({error:"Failed to create order"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/orders/route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:v,serverHooks:g}=m;function h(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:v})}},38629:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var i=t(65064);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var u=i?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(a,s,u):a[s]=e[s]}return a.default=e,t&&t.set(e,a),a}(t(35003));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65064:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},70721:(e,r,t)=>{"use strict";t.d(r,{$:()=>m});var a=t(83761),i=t(53829),s=t(95842),n=t(32967),u=t(20331),o=t(75758),d=t(17773),c=t(97641);t(35003);let l=(0,o.A)({id:"google-one-tap",name:"Google One Tap",credentials:{credential:{type:"text"}},async authorize(e){try{if(!e?.credential)return null;let r=(0,d.s)(e.credential),t={uuid:r.sub,email:r.email,nickname:r.name,avatarUrl:r.picture,signinType:"oauth",signinIp:"127.0.0.1",signinProvider:"google-one-tap",signinOpenid:r.sub,createdAt:new Date},a=await s.zR.user.findFirst({where:{email:r.email,signinProvider:"google-one-tap"}});return a?await s.zR.user.update({where:{id:a.id},data:{nickname:t.nickname,avatarUrl:t.avatarUrl}}):await s.zR.user.create({data:t}),{id:r.sub,email:r.email,name:r.name,image:r.picture,uuid:r.sub}}catch(e){return null}}}),p=(0,o.A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await (0,s.iA)(e.email,"credentials");return r&&r.password&&await (0,c.UD)(e.password,r.password)?{id:r.id.toString(),email:r.email,name:r.nickname||"",image:r.avatarUrl||"",uuid:r.uuid}:null}}),m={providers:[...process.env.AUTH_GITHUB_ID&&process.env.AUTH_GITHUB_SECRET?[(0,i.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET})]:[],...process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET?[(0,a.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET})]:[],l,p],debug:!1,secret:process.env.AUTH_SECRET,cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},callbackUrl:{name:"next-auth.callback-url",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},csrfToken:{name:"next-auth.csrf-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},pkceCodeVerifier:{name:"next-auth.pkce.code_verifier",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}},state:{name:"next-auth.state",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}}},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.id=r.id,"uuid"in r&&(e.uuid=r.uuid)),r&&t&&"credentials"!==t.provider)try{let a=await s.zR.user.findFirst({where:{email:r.email||"",signinProvider:t.provider}});a||(a=await s.zR.user.create({data:{uuid:(0,n.A)(),email:r.email||"",nickname:r.name||"",avatarUrl:r.image||"",signinProvider:t.provider,signinOpenid:t.providerAccountId}})),e.id=a.id.toString(),e.uuid=a.uuid}catch(e){}return e},session:async({session:e,token:r})=>(e.user&&r&&(e.user.id=r.id,e.user.uuid=r.uuid),e),async signIn({user:e,account:r,profile:t}){try{if(!e.email)return!1;let a="127.0.0.1";try{let e=(await (0,u.headers)()).get("x-forwarded-for");a=e?e.split(",")[0]:"127.0.0.1"}catch(e){}let i={uuid:(0,n.A)(),email:e.email,nickname:t?.name||e.name,avatarUrl:t?.picture||t?.avatar_url||e.image,signinType:"oauth",signinIp:a,signinProvider:r?.provider,signinOpenid:r?.providerAccountId,createdAt:new Date},o=await s.zR.user.findFirst({where:{email:e.email,signinProvider:r?.provider}});return o?await s.zR.user.update({where:{id:o.id},data:{nickname:i.nickname,avatarUrl:i.avatarUrl,signinIp:a}}):await s.zR.user.create({data:i}),!0}catch(e){return e instanceof Error,!0}}},pages:{signIn:"/auth/signin",error:"/auth/signin"},session:{strategy:"jwt",maxAge:2592e3}}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95842:(e,r,t)=>{"use strict";t.d(r,{Jn:()=>n,iA:()=>s,zR:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient;async function s(e,r){return await i.user.findFirst({where:{email:e,signinProvider:r,isDeleted:!1}})}async function n(e){return await i.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[883,331,248,47,690],()=>t(37867));module.exports=a})();