(()=>{var e={};e.id=653,e.ids=[653],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},38629:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var a=t(65064);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(i,s,u):i[s]=e[s]}return i.default=e,t&&t.set(e,i),i}(t(35003));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65064:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0})},70150:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>v,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var i={};t.r(i),t.d(i,{POST:()=>f});var a=t(36331),s=t(62420),n=t(88267),u=t(91930),o=t(2231),c=t(95842),p=t(38629),d=t(70721),l=t(32967);if(!process.env.STRIPE_PRIVATE_KEY)throw Error("STRIPE_PRIVATE_KEY is not set");let m=new o.A(process.env.STRIPE_PRIVATE_KEY,{apiVersion:"2025-02-24.acacia"});async function f(e){try{let r=await (0,p.getServerSession)(d.$);if(!r?.user?.email)return u.NextResponse.json({error:"Authentication required"},{status:401});let{price:t,successUrl:i,cancelUrl:a,email:s,productName:n}=await e.json(),o=Math.round(100*parseFloat(t));if(isNaN(o))return u.NextResponse.json({error:"Invalid price amount"},{status:400});let f=await c.zR.user.findFirst({where:{email:r.user.email}});if(!f)return u.NextResponse.json({error:"User not found"},{status:404});let v=await m.checkout.sessions.create({payment_method_types:["card"],customer_email:s,line_items:[{price_data:{currency:"usd",product_data:{name:n||"Purchase"},unit_amount:o},quantity:1}],mode:"payment",success_url:i,cancel_url:a});return await c.zR.order.create({data:{orderNo:(0,l.A)(),userUuid:f.uuid,userEmail:f.email,amount:o,status:"pending",stripeSessionId:v.id,credits:1,currency:"usd",productName:n||"Purchase",createdAt:new Date}}),u.NextResponse.json({url:v.url})}catch(e){return u.NextResponse.json({error:e.message},{status:500})}}let v=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/stripe/route",pathname:"/api/stripe",filename:"route",bundlePath:"app/api/stripe/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/stripe/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:w}=v;function y(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},70721:(e,r,t)=>{"use strict";t.d(r,{$:()=>m});var i=t(83761),a=t(53829),s=t(95842),n=t(32967),u=t(20331),o=t(75758),c=t(17773),p=t(97641);t(35003);let d=(0,o.A)({id:"google-one-tap",name:"Google One Tap",credentials:{credential:{type:"text"}},async authorize(e){try{if(!e?.credential)return null;let r=(0,c.s)(e.credential),t={uuid:r.sub,email:r.email,nickname:r.name,avatarUrl:r.picture,signinType:"oauth",signinIp:"127.0.0.1",signinProvider:"google-one-tap",signinOpenid:r.sub,createdAt:new Date},i=await s.zR.user.findFirst({where:{email:r.email,signinProvider:"google-one-tap"}});return i?await s.zR.user.update({where:{id:i.id},data:{nickname:t.nickname,avatarUrl:t.avatarUrl}}):await s.zR.user.create({data:t}),{id:r.sub,email:r.email,name:r.name,image:r.picture,uuid:r.sub}}catch(e){return null}}}),l=(0,o.A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await (0,s.iA)(e.email,"credentials");return r&&r.password&&await (0,p.UD)(e.password,r.password)?{id:r.id.toString(),email:r.email,name:r.nickname||"",image:r.avatarUrl||"",uuid:r.uuid}:null}}),m={providers:[...process.env.AUTH_GITHUB_ID&&process.env.AUTH_GITHUB_SECRET?[(0,a.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET})]:[],...process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET?[(0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET})]:[],d,l],debug:!1,secret:process.env.AUTH_SECRET,cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},callbackUrl:{name:"next-auth.callback-url",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},csrfToken:{name:"next-auth.csrf-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},pkceCodeVerifier:{name:"next-auth.pkce.code_verifier",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}},state:{name:"next-auth.state",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}}},callbacks:{async jwt({token:e,user:r,account:t}){if(r&&(e.id=r.id,"uuid"in r&&(e.uuid=r.uuid)),r&&t&&"credentials"!==t.provider)try{let i=await s.zR.user.findFirst({where:{email:r.email||"",signinProvider:t.provider}});i||(i=await s.zR.user.create({data:{uuid:(0,n.A)(),email:r.email||"",nickname:r.name||"",avatarUrl:r.image||"",signinProvider:t.provider,signinOpenid:t.providerAccountId}})),e.id=i.id.toString(),e.uuid=i.uuid}catch(e){}return e},session:async({session:e,token:r})=>(e.user&&r&&(e.user.id=r.id,e.user.uuid=r.uuid),e),async signIn({user:e,account:r,profile:t}){try{if(!e.email)return!1;let i="127.0.0.1";try{let e=(await (0,u.headers)()).get("x-forwarded-for");i=e?e.split(",")[0]:"127.0.0.1"}catch(e){}let a={uuid:(0,n.A)(),email:e.email,nickname:t?.name||e.name,avatarUrl:t?.picture||t?.avatar_url||e.image,signinType:"oauth",signinIp:i,signinProvider:r?.provider,signinOpenid:r?.providerAccountId,createdAt:new Date},o=await s.zR.user.findFirst({where:{email:e.email,signinProvider:r?.provider}});return o?await s.zR.user.update({where:{id:o.id},data:{nickname:a.nickname,avatarUrl:a.avatarUrl,signinIp:i}}):await s.zR.user.create({data:a}),!0}catch(e){return e instanceof Error,!0}}},pages:{signIn:"/auth/signin",error:"/auth/signin"},session:{strategy:"jwt",maxAge:2592e3}}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95842:(e,r,t)=>{"use strict";t.d(r,{Jn:()=>n,iA:()=>s,zR:()=>a});let i=require("@prisma/client"),a=globalThis.prisma??new i.PrismaClient;async function s(e,r){return await a.user.findFirst({where:{email:e,signinProvider:r,isDeleted:!1}})}async function n(e){return await a.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[883,331,248,47,690,231],()=>t(70150));module.exports=i})();