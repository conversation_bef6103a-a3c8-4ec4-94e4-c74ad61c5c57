(()=>{var e={};e.id=287,e.ids=[287],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52123:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>c});var i=t(36331),a=t(62420),o=t(88267),n=t(91930),d=t(2231),u=t(95842);if(!process.env.STRIPE_PRIVATE_KEY||!process.env.STRIPE_WEBHOOK_SECRET)throw Error("Missing Stripe environment variables");let p=new d.A(process.env.STRIPE_PRIVATE_KEY,{apiVersion:"2025-02-24.acacia"});async function c(e){try{let r=await e.text(),t=e.headers.get("stripe-signature");if(!t)return n.NextResponse.json({error:"No signature"},{status:400});let s=p.webhooks.constructEvent(r,t,process.env.STRIPE_WEBHOOK_SECRET);switch(s.type){case"checkout.session.completed":{let e=s.data.object,r=await u.zR.order.findFirst({where:{stripeSessionId:e.id}});if(!r)return n.NextResponse.json({error:"Order not found"},{status:404});await u.zR.order.update({where:{orderNo:r.orderNo},data:{status:"paid",paidAt:new Date,paidEmail:e.customer_email||void 0,paidDetail:JSON.stringify(e)}});break}case"checkout.session.expired":{let e=s.data.object,r=await u.zR.order.findFirst({where:{stripeSessionId:e.id}});if(!r)return n.NextResponse.json({error:"Order not found"},{status:404});await u.zR.order.update({where:{orderNo:r.orderNo},data:{status:"expired",paidDetail:JSON.stringify(e)}});break}case"payment_intent.payment_failed":{let e=s.data.object,r=await p.checkout.sessions.retrieve(e.metadata.session_id),t=await u.zR.order.findFirst({where:{stripeSessionId:r.id}});if(!t)return n.NextResponse.json({error:"Order not found"},{status:404});await u.zR.order.update({where:{orderNo:t.orderNo},data:{status:"failed",paidDetail:JSON.stringify(e)}})}}return n.NextResponse.json({received:!0})}catch(e){return n.NextResponse.json({error:"Webhook signature verification failed"},{status:400})}}let l=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/stripe/webhook/route",pathname:"/api/stripe/webhook",filename:"route",bundlePath:"app/api/stripe/webhook/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/stripe/webhook/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:h,serverHooks:x}=l;function f(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:h})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95842:(e,r,t)=>{"use strict";t.d(r,{Jn:()=>o,iA:()=>a,zR:()=>i});let s=require("@prisma/client"),i=globalThis.prisma??new s.PrismaClient;async function a(e,r){return await i.user.findFirst({where:{email:e,signinProvider:r,isDeleted:!1}})}async function o(e){return await i.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[883,248,231],()=>t(52123));module.exports=s})();