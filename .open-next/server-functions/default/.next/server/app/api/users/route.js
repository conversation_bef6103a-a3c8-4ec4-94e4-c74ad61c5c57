(()=>{var e={};e.id=318,e.ids=[318],e.modules={3110:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{GET:()=>p,POST:()=>d});var i=s(36331),n=s(62420),a=s(88267),o=s(91930),u=s(95842);async function p(){try{let e=await u.zR.user.findMany({include:{orders:!0}});return o.NextResponse.json(e)}catch(e){return o.NextResponse.json({error:"Failed to fetch users"},{status:500})}}async function d(e){try{let r=await e.j<PERSON>(),s=await u.zR.user.create({data:{uuid:r.uuid,email:r.email,nickname:r.nickname,avatarUrl:r.avatarUrl,locale:r.locale,signinType:r.signinType,signinIp:r.signinIp,signinProvider:r.signinProvider,signinOpenid:r.signinOpenid}});return o.NextResponse.json(s)}catch(e){return o.NextResponse.json({error:"Failed to create user"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/users/route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:x}=c;function m(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},95842:(e,r,s)=>{"use strict";s.d(r,{Jn:()=>a,iA:()=>n,zR:()=>i});let t=require("@prisma/client"),i=globalThis.prisma??new t.PrismaClient;async function n(e,r){return await i.user.findFirst({where:{email:e,signinProvider:r,isDeleted:!1}})}async function a(e){return await i.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[883,248],()=>s(3110));module.exports=t})();