(()=>{var e={};e.id=461,e.ids=[461],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13142:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var i={};r.r(i),r.d(i,{GET:()=>p});var a=r(36331),s=r(62420),n=r(88267),o=r(91930),u=r(38629),c=r(70721);async function p(){try{let e=await (0,u.getServerSession)(c.$);if(!e?.user?.email)return o.NextResponse.json({error:"Unauthorized"},{status:401});let t=process.env.GITHUB_API_TOKEN,r={tokenConfigured:!!t};if(!t)return r.error="GitHub API token not configured in environment variables",o.NextResponse.json(r);try{let e=await fetch("https://api.github.com/user",{headers:{Accept:"application/vnd.github+json",Authorization:`Bearer ${t}`,"X-GitHub-Api-Version":"2022-11-28","User-Agent":"ShipSaaS-App/1.0.0"}});if(!e.ok)return r.error=`Failed to authenticate with GitHub: ${e.status} ${e.statusText}`,o.NextResponse.json(r);{r.user=await e.json();let t=e.headers.get("X-OAuth-Scopes");r.scopes=t?t.split(", "):[]}let i=await fetch("https://api.github.com/repos/ShipSaaSCo/shipsaas-starter",{headers:{Accept:"application/vnd.github+json",Authorization:`Bearer ${t}`,"X-GitHub-Api-Version":"2022-11-28","User-Agent":"ShipSaaS-App/1.0.0"}});i.ok?r.repositoryAccess=await i.json():r.error=`Cannot access repository: ${i.status} ${i.statusText}`;let a=await fetch("https://api.github.com/rate_limit",{headers:{Accept:"application/vnd.github+json",Authorization:`Bearer ${t}`,"X-GitHub-Api-Version":"2022-11-28","User-Agent":"ShipSaaS-App/1.0.0"}});a.ok&&(r.rateLimit=await a.json())}catch(e){r.error=`Error checking GitHub permissions: ${e.message}`}return o.NextResponse.json(r)}catch(e){return o.NextResponse.json({error:"Failed to check GitHub permissions"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/github/check-permissions/route",pathname:"/api/github/check-permissions",filename:"route",bundlePath:"app/api/github/check-permissions/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/github/check-permissions/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:m}=l;function g(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},38629:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var a=r(65064);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var o=a?Object.getOwnPropertyDescriptor(e,s):null;o&&(o.get||o.set)?Object.defineProperty(i,s,o):i[s]=e[s]}return i.default=e,r&&r.set(e,i),i}(r(35003));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65064:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},70721:(e,t,r)=>{"use strict";r.d(t,{$:()=>h});var i=r(83761),a=r(53829),s=r(95842),n=r(32967),o=r(20331),u=r(75758),c=r(17773),p=r(97641);r(35003);let l=(0,u.A)({id:"google-one-tap",name:"Google One Tap",credentials:{credential:{type:"text"}},async authorize(e){try{if(!e?.credential)return null;let t=(0,c.s)(e.credential),r={uuid:t.sub,email:t.email,nickname:t.name,avatarUrl:t.picture,signinType:"oauth",signinIp:"127.0.0.1",signinProvider:"google-one-tap",signinOpenid:t.sub,createdAt:new Date},i=await s.zR.user.findFirst({where:{email:t.email,signinProvider:"google-one-tap"}});return i?await s.zR.user.update({where:{id:i.id},data:{nickname:r.nickname,avatarUrl:r.avatarUrl}}):await s.zR.user.create({data:r}),{id:t.sub,email:t.email,name:t.name,image:t.picture,uuid:t.sub}}catch(e){return null}}}),d=(0,u.A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await (0,s.iA)(e.email,"credentials");return t&&t.password&&await (0,p.UD)(e.password,t.password)?{id:t.id.toString(),email:t.email,name:t.nickname||"",image:t.avatarUrl||"",uuid:t.uuid}:null}}),h={providers:[...process.env.AUTH_GITHUB_ID&&process.env.AUTH_GITHUB_SECRET?[(0,a.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET})]:[],...process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET?[(0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET})]:[],l,d],debug:!1,secret:process.env.AUTH_SECRET,cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},callbackUrl:{name:"next-auth.callback-url",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},csrfToken:{name:"next-auth.csrf-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},pkceCodeVerifier:{name:"next-auth.pkce.code_verifier",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}},state:{name:"next-auth.state",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}}},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.id=t.id,"uuid"in t&&(e.uuid=t.uuid)),t&&r&&"credentials"!==r.provider)try{let i=await s.zR.user.findFirst({where:{email:t.email||"",signinProvider:r.provider}});i||(i=await s.zR.user.create({data:{uuid:(0,n.A)(),email:t.email||"",nickname:t.name||"",avatarUrl:t.image||"",signinProvider:r.provider,signinOpenid:r.providerAccountId}})),e.id=i.id.toString(),e.uuid=i.uuid}catch(e){}return e},session:async({session:e,token:t})=>(e.user&&t&&(e.user.id=t.id,e.user.uuid=t.uuid),e),async signIn({user:e,account:t,profile:r}){try{if(!e.email)return!1;let i="127.0.0.1";try{let e=(await (0,o.headers)()).get("x-forwarded-for");i=e?e.split(",")[0]:"127.0.0.1"}catch(e){}let a={uuid:(0,n.A)(),email:e.email,nickname:r?.name||e.name,avatarUrl:r?.picture||r?.avatar_url||e.image,signinType:"oauth",signinIp:i,signinProvider:t?.provider,signinOpenid:t?.providerAccountId,createdAt:new Date},u=await s.zR.user.findFirst({where:{email:e.email,signinProvider:t?.provider}});return u?await s.zR.user.update({where:{id:u.id},data:{nickname:a.nickname,avatarUrl:a.avatarUrl,signinIp:i}}):await s.zR.user.create({data:a}),!0}catch(e){return e instanceof Error,!0}}},pages:{signIn:"/auth/signin",error:"/auth/signin"},session:{strategy:"jwt",maxAge:2592e3}}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95842:(e,t,r)=>{"use strict";r.d(t,{Jn:()=>n,iA:()=>s,zR:()=>a});let i=require("@prisma/client"),a=globalThis.prisma??new i.PrismaClient;async function s(e,t){return await a.user.findFirst({where:{email:e,signinProvider:t,isDeleted:!1}})}async function n(e){return await a.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[883,331,248,47,690],()=>r(13142));module.exports=i})();