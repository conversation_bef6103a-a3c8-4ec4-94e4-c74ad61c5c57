(()=>{var e={};e.id=341,e.ids=[341],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12036:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{POST:()=>c});var s=r(36331),a=r(62420),n=r(88267),o=r(91930),u=r(95842),d=r(38629),p=r(70721);async function c(e){try{let t=await (0,d.getServerSession)(p.$);if(!t?.user?.email)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{orderNo:r,githubUsername:i}=await e.json();if(!r||!i)return o.NextResponse.json({error:"Order number and GitHub username are required"},{status:400});if(!/^[a-z\d](?:[a-z\d]|-(?=[a-z\d])){0,38}$/i.test(i))return o.NextResponse.json({error:"Invalid GitHub username format"},{status:400});let s=await u.zR.user.findFirst({where:{email:t.user.email}});if(!s)return o.NextResponse.json({error:"User not found"},{status:404});let a=await u.zR.order.findFirst({where:{orderNo:r,userUuid:s.uuid}});if(!a)return o.NextResponse.json({error:"Order not found"},{status:404});if("activated"!==a.status)return o.NextResponse.json({error:"Order must be activated before sending GitHub invitation"},{status:400});let n=process.env.GITHUB_API_TOKEN;if(!n)return o.NextResponse.json({error:"GitHub integration not configured"},{status:500});let c=(e=>{let t=e.toLowerCase();return t.includes("starter")||t.includes("基础版")?"shipsaas-starter":t.includes("pro")||t.includes("专业版")?"shipsaas-standard":t.includes("enterprise")||t.includes("旗舰版")?"shipsaas-enterprise":"shipsaas-starter"})(a.productName||""),l=`https://api.github.com/repos/ShipSaaSCo/${c}/collaborators/${i}`,m=await fetch(l,{method:"PUT",headers:{Accept:"application/vnd.github+json",Authorization:`Bearer ${n}`,"Content-Type":"application/json","X-GitHub-Api-Version":"2022-11-28","User-Agent":"ShipSaaS-App/1.0.0"},body:JSON.stringify({permission:"pull"})});if(!m.ok){if(await m.json().catch(()=>({})),404===m.status)return o.NextResponse.json({error:"GitHub username not found or repository not accessible. Please check the username and ensure the repository exists."},{status:400});if(403===m.status)return o.NextResponse.json({error:"Insufficient permissions. The GitHub token may not have admin access to the repository."},{status:500});if(422===m.status)return o.NextResponse.json({error:"Validation failed. The user may already be a collaborator or the request is invalid."},{status:400});return o.NextResponse.json({error:`GitHub API error: ${m.status} ${m.statusText}`},{status:500})}return 201===m.status?await m.json():m.status,await u.zR.order.update({where:{orderNo:r},data:{orderDetail:JSON.stringify({...JSON.parse(a.orderDetail||"{}"),githubUsername:i,repositoryName:c,invitationSentAt:new Date().toISOString()}),updatedAt:new Date}}),o.NextResponse.json({success:!0,message:"GitHub invitation sent successfully",invitationUrl:`https://github.com/ShipSaaSCo/${c}/invitations`,repositoryName:c})}catch(e){return o.NextResponse.json({error:"Failed to send GitHub invitation"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/github/invite/route",pathname:"/api/github/invite",filename:"route",bundlePath:"app/api/github/invite/route"},resolvedPagePath:"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/api/github/invite/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:h}=l;function v(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:()=>{},38629:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var s=r(65064);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var o=s?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(i,a,o):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}(r(35003));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59903:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65064:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},70721:(e,t,r)=>{"use strict";r.d(t,{$:()=>m});var i=r(83761),s=r(53829),a=r(95842),n=r(32967),o=r(20331),u=r(75758),d=r(17773),p=r(97641);r(35003);let c=(0,u.A)({id:"google-one-tap",name:"Google One Tap",credentials:{credential:{type:"text"}},async authorize(e){try{if(!e?.credential)return null;let t=(0,d.s)(e.credential),r={uuid:t.sub,email:t.email,nickname:t.name,avatarUrl:t.picture,signinType:"oauth",signinIp:"127.0.0.1",signinProvider:"google-one-tap",signinOpenid:t.sub,createdAt:new Date},i=await a.zR.user.findFirst({where:{email:t.email,signinProvider:"google-one-tap"}});return i?await a.zR.user.update({where:{id:i.id},data:{nickname:r.nickname,avatarUrl:r.avatarUrl}}):await a.zR.user.create({data:r}),{id:t.sub,email:t.email,name:t.name,image:t.picture,uuid:t.sub}}catch(e){return null}}}),l=(0,u.A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await (0,a.iA)(e.email,"credentials");return t&&t.password&&await (0,p.UD)(e.password,t.password)?{id:t.id.toString(),email:t.email,name:t.nickname||"",image:t.avatarUrl||"",uuid:t.uuid}:null}}),m={providers:[...process.env.AUTH_GITHUB_ID&&process.env.AUTH_GITHUB_SECRET?[(0,s.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET})]:[],...process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET?[(0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET})]:[],c,l],debug:!1,secret:process.env.AUTH_SECRET,cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},callbackUrl:{name:"next-auth.callback-url",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},csrfToken:{name:"next-auth.csrf-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},pkceCodeVerifier:{name:"next-auth.pkce.code_verifier",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}},state:{name:"next-auth.state",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}}},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.id=t.id,"uuid"in t&&(e.uuid=t.uuid)),t&&r&&"credentials"!==r.provider)try{let i=await a.zR.user.findFirst({where:{email:t.email||"",signinProvider:r.provider}});i||(i=await a.zR.user.create({data:{uuid:(0,n.A)(),email:t.email||"",nickname:t.name||"",avatarUrl:t.image||"",signinProvider:r.provider,signinOpenid:r.providerAccountId}})),e.id=i.id.toString(),e.uuid=i.uuid}catch(e){}return e},session:async({session:e,token:t})=>(e.user&&t&&(e.user.id=t.id,e.user.uuid=t.uuid),e),async signIn({user:e,account:t,profile:r}){try{if(!e.email)return!1;let i="127.0.0.1";try{let e=(await (0,o.headers)()).get("x-forwarded-for");i=e?e.split(",")[0]:"127.0.0.1"}catch(e){}let s={uuid:(0,n.A)(),email:e.email,nickname:r?.name||e.name,avatarUrl:r?.picture||r?.avatar_url||e.image,signinType:"oauth",signinIp:i,signinProvider:t?.provider,signinOpenid:t?.providerAccountId,createdAt:new Date},u=await a.zR.user.findFirst({where:{email:e.email,signinProvider:t?.provider}});return u?await a.zR.user.update({where:{id:u.id},data:{nickname:s.nickname,avatarUrl:s.avatarUrl,signinIp:i}}):await a.zR.user.create({data:s}),!0}catch(e){return e instanceof Error,!0}}},pages:{signIn:"/auth/signin",error:"/auth/signin"},session:{strategy:"jwt",maxAge:2592e3}}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95842:(e,t,r)=>{"use strict";r.d(t,{Jn:()=>n,iA:()=>a,zR:()=>s});let i=require("@prisma/client"),s=globalThis.prisma??new i.PrismaClient;async function a(e,t){return await s.user.findFirst({where:{email:e,signinProvider:t,isDeleted:!1}})}async function n(e){return await s.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[883,331,248,47,690],()=>r(12036));module.exports=i})();