"use strict";(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7866:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return u}});let n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var i=a?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(n,s,i):n[s]=e[s]}return n.default=e,t&&t.set(e,n),n}(t(53764));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}let a={current:null},s="function"==typeof n.cache?n.cache:e=>e,i=console.warn;function u(e){return function(...r){i(e(...r))}}s(e=>{try{i(a.current)}finally{a.current=null}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},6e4:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>c,tree:()=>p});var n=t(16507),o=t(62420),a=t(66550),s=t.n(a),i=t(99937),u={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>i[e]);t.d(r,u);let p={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=[],d={require:t,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[883,848,268],()=>t(6e4));module.exports=n})();