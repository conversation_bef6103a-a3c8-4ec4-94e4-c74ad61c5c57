(()=>{var e={};e.id=483,e.ids=[483],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11554:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=r(16507),a=r(62420),i=r(66550),d=r.n(i),n=r(99937),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let o={children:["",{children:["[locale]",{children:["demo",{children:["multi-plan-github",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49681)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/demo/multi-plan-github/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10746)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,77858)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/demo/multi-plan-github/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/demo/multi-plan-github/page",pathname:"/[locale]/demo/multi-plan-github",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29558:(e,t,r)=>{Promise.resolve().then(r.bind(r,48258))},30588:(e,t,r)=>{"use strict";r.d(t,{MultiPlanGitHubDemo:()=>s});let s=(0,r(57479).registerClientReference)(function(){throw Error("Attempted to call MultiPlanGitHubDemo() from the server but MultiPlanGitHubDemo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/demo/MultiPlanGitHubDemo.tsx","MultiPlanGitHubDemo")},32915:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(91164).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},33873:e=>{"use strict";e.exports=require("path")},48258:(e,t,r)=>{"use strict";r.d(t,{MultiPlanGitHubDemo:()=>p});var s=r(43147),a=r(26926),i=r(30630),d=r(56783),n=r(59241),l=r(32915),o=r(68739),c=r(53119),u=r(55802),x=r(73282);function p(){let[e,t]=(0,a.useState)(!1),[r,p]=(0,a.useState)(null),m=e=>{switch(e.toLowerCase()){case"paid":return"bg-blue-500 hover:bg-blue-600";case"activated":return"bg-green-500 hover:bg-green-600";case"pending":return"bg-yellow-500 hover:bg-yellow-600";default:return"bg-gray-500 hover:bg-gray-600"}},h=(e,t)=>new Intl.NumberFormat("en-US",{style:"currency",currency:t||"USD"}).format(e/100),g=e=>({paid:"Paid",activated:"Activated",pending:"Pending",failed:"Failed",expired:"Expired"})[e.toLowerCase()]||e,b=e=>{p(e),t(!0)},y=e=>{let t=e.toLowerCase();return t.includes("starter")?"from-blue-500 to-blue-600":t.includes("pro")?"from-purple-500 to-purple-600":t.includes("enterprise")?"from-orange-500 to-orange-600":"from-gray-500 to-gray-600"};return(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-6xl",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Multi-Plan GitHub Repository Access"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Different pricing plans get access to different GitHub repositories"})]}),(0,s.jsx)("div",{className:"grid gap-6 md:grid-cols-1 lg:grid-cols-3",children:[{id:1,orderNo:"ORD-2024-001",amount:9900,status:"paid",createdAt:"2024-01-15T10:30:00Z",productName:"Starter - monthly",currency:"USD",paidAt:"2024-01-15T10:35:00Z",expectedRepository:"shipsaas-starter"},{id:2,orderNo:"ORD-2024-002",amount:19900,status:"paid",createdAt:"2024-01-10T14:20:00Z",productName:"Pro - monthly",currency:"USD",paidAt:"2024-01-10T14:25:00Z",expectedRepository:"shipsaas-standard"},{id:3,orderNo:"ORD-2024-003",amount:29900,status:"paid",createdAt:"2024-01-20T09:15:00Z",productName:"Enterprise - monthly",currency:"USD",paidAt:"2024-01-20T09:20:00Z",expectedRepository:"shipsaas-enterprise"}].map(e=>(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2 text-gray-900 dark:text-white",children:e.productName}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Order ID: ",e.orderNo]})]}),(0,s.jsx)(i.E,{className:`${m(e.status)} text-white border-0`,children:g(e.status)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Amount"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:h(e.amount,e.currency)})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Order Date"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:(0,u.GP)(new Date(e.createdAt),"PPP")})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Paid Date"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:(0,u.GP)(new Date(e.paidAt),"PPP")})]})]})]}),(0,s.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:"Target Repository:"}),(0,s.jsxs)("p",{className:"text-sm font-mono text-gray-800 dark:text-gray-200",children:["ShipSaaSCo/",e.expectedRepository]})]}),(0,s.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,s.jsx)(c.A,{className:"w-4 h-4 text-green-500"}),(0,s.jsx)("span",{children:"Payment confirmed"})]}),(0,s.jsxs)(d.$,{onClick:()=>b(e),className:`bg-gradient-to-r ${y(e.productName)} hover:opacity-90 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105`,children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Activate Order"]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:"Click to activate and get GitHub repository access"})]})]},e.orderNo))}),(0,s.jsxs)("div",{className:"mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100",children:"Repository Mapping"}),(0,s.jsxs)("div",{className:"grid gap-3 md:grid-cols-3",children:[(0,s.jsxs)("div",{className:"bg-white dark:bg-blue-800/30 p-3 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-800 dark:text-blue-200",children:"Starter Plan"}),(0,s.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-300 font-mono",children:"ShipSaaSCo/shipsaas-starter"}),(0,s.jsx)("p",{className:"text-xs text-blue-500 dark:text-blue-400 mt-1",children:"Basic features and templates"})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-blue-800/30 p-3 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-800 dark:text-blue-200",children:"Pro Plan"}),(0,s.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-300 font-mono",children:"ShipSaaSCo/shipsaas-standard"}),(0,s.jsx)("p",{className:"text-xs text-blue-500 dark:text-blue-400 mt-1",children:"Advanced features and integrations"})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-blue-800/30 p-3 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-800 dark:text-blue-200",children:"Enterprise Plan"}),(0,s.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-300 font-mono",children:"ShipSaaSCo/shipsaas-enterprise"}),(0,s.jsx)("p",{className:"text-xs text-blue-500 dark:text-blue-400 mt-1",children:"Enterprise features and support"})]})]})]}),r&&(0,s.jsx)(x.a,{isOpen:e,onClose:()=>{t(!1),p(null)},orderNo:r.orderNo,productName:r.productName})]})})}},49681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(21393),a=r(30588);function i(){return(0,s.jsx)(a.MultiPlanGitHubDemo,{})}},59241:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(91164).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68739:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(91164).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},89806:(e,t,r)=>{Promise.resolve().then(r.bind(r,30588))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[883,331,848,540,432,268,585,282],()=>r(11554));module.exports=s})();