(()=>{var e={};e.id=280,e.ids=[280],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8243:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(91164).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8818:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(91164).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11546:(e,s,r)=>{Promise.resolve().then(r.bind(r,67843))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32170:(e,s,r)=>{"use strict";r.d(s,{GitHubPermissionCheck:()=>t});let t=(0,r(57479).registerClientReference)(function(){throw Error("Attempted to call GitHubPermissionCheck() from the server but GitHubPermissionCheck is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/demo/GitHubPermissionCheck.tsx","GitHubPermissionCheck")},33873:e=>{"use strict";e.exports=require("path")},40402:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var t=r(16507),i=r(62420),a=r(66550),n=r.n(a),o=r(99937),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let c={children:["",{children:["[locale]",{children:["demo",{children:["github-permissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56892)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/demo/github-permissions/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10746)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,77858)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/demo/github-permissions/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/demo/github-permissions/page",pathname:"/[locale]/demo/github-permissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48338:(e,s,r)=>{Promise.resolve().then(r.bind(r,32170))},53119:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(91164).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},56892:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(21393),i=r(32170);function a(){return(0,t.jsx)(i.GitHubPermissionCheck,{})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67843:(e,s,r)=>{"use strict";r.d(s,{GitHubPermissionCheck:()=>m});var t=r(43147),i=r(26926),a=r(56783),n=r(53119);let o=(0,r(91164).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var l=r(90770),c=r(8243),d=r(8818);function m(){var e;let[s,r]=(0,i.useState)(!1),[m,h]=(0,i.useState)(null),x=async()=>{r(!0),h(null);try{let e=await fetch("/api/github/check-permissions"),s=await e.json();h(s)}catch(e){h({error:"Failed to check permissions"})}finally{r(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,t.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"GitHub Permission Check"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Check GitHub API token permissions and repository access for debugging"})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Permission Status"}),(0,t.jsx)(a.$,{onClick:x,disabled:s,className:"bg-gradient-to-r from-gray-900 to-gray-700 hover:from-gray-800 hover:to-gray-600",children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Checking..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Check Permissions"]})})]}),m&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[!0===(e=m.tokenConfigured)?(0,t.jsx)(n.A,{className:"w-5 h-5 text-green-500"}):!1===e?(0,t.jsx)(o,{className:"w-5 h-5 text-red-500"}):(0,t.jsx)(l.A,{className:"w-5 h-5 text-yellow-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"GitHub Token Configuration"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:m.tokenConfigured?"Token is configured":"Token not found in environment"})]})]}),m.user&&(0,t.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 text-green-500"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"GitHub User Authentication"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Authenticated as: ",(0,t.jsx)("strong",{children:m.user.login})," (",m.user.name||"No name",")"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["User ID: ",m.user.id," | Type: ",m.user.type]})]})]}),m.scopes&&(0,t.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 text-blue-500"}),(0,t.jsx)("p",{className:"font-medium",children:"Token Scopes"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:m.scopes.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs rounded-full",children:e},e))}),(0,t.jsxs)("div",{className:"mt-2 text-xs text-gray-600 dark:text-gray-400",children:["Required scopes: ",(0,t.jsx)("strong",{children:"repo"}),", ",(0,t.jsx)("strong",{children:"admin:org"})," (for organization repositories)"]})]}),m.repositoryAccess&&(0,t.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 text-green-500"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"Repository Access"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Can access: ",(0,t.jsx)("strong",{children:m.repositoryAccess.full_name})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Private: ",m.repositoryAccess.private?"Yes":"No"," | Permissions: ",JSON.stringify(m.repositoryAccess.permissions||{})]})]})]}),m.rateLimit&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(l.A,{className:"w-5 h-5 text-yellow-500"}),(0,t.jsx)("p",{className:"font-medium",children:"Rate Limit Status"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Core API"}),(0,t.jsxs)("p",{className:"font-medium",children:[m.rateLimit.resources.core.remaining," / ",m.rateLimit.resources.core.limit]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Search API"}),(0,t.jsxs)("p",{className:"font-medium",children:[m.rateLimit.resources.search.remaining," / ",m.rateLimit.resources.search.limit]})]})]})]}),m.error&&(0,t.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg",children:[(0,t.jsx)(o,{className:"w-5 h-5 text-red-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-red-800 dark:text-red-200",children:"Error"}),(0,t.jsx)("p",{className:"text-sm text-red-600 dark:text-red-300",children:m.error})]})]})]}),(0,t.jsxs)("div",{className:"mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold mb-2 text-yellow-800 dark:text-yellow-200",children:"Setup Instructions"}),(0,t.jsxs)("div",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,t.jsx)("p",{children:"1. Create a GitHub Personal Access Token at: https://github.com/settings/tokens"}),(0,t.jsx)("p",{children:"2. Grant the following permissions:"}),(0,t.jsxs)("ul",{className:"ml-4 list-disc",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"repo"})," - Full control of private repositories"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"admin:org"})," - Full control of orgs and teams (for organization repositories)"]})]}),(0,t.jsxs)("p",{children:["3. Add the token to your .env file: ",(0,t.jsx)("code",{children:"GITHUB_API_TOKEN=your_token_here"})]}),(0,t.jsx)("p",{children:"4. Ensure the token owner has admin access to the target repository"}),(0,t.jsx)("p",{children:"5. Restart your development server after adding the token"})]})]})]})]})})}},90770:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(91164).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[883,331,848,540,268,585],()=>r(40402));module.exports=t})();