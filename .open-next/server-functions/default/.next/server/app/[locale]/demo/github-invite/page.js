(()=>{var e={};e.id=873,e.ids=[873],e.modules={3026:(e,t,r)=>{Promise.resolve().then(r.bind(r,62019))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23921:(e,t,r)=>{"use strict";r.d(t,{GitHubInviteDemo:()=>c});var s=r(43147),i=r(30630),a=r(59241),n=r(32915),o=r(68739),d=r(53119),l=r(55802);function c(){return(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"GitHub Invite Demo"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Demonstration of GitHub repository invitation functionality for activated orders"})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2 text-gray-900 dark:text-white",children:"Pro Plan - Monthly"}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Order ID: ","ORD-2024-001"]})]}),(0,s.jsx)(i.E,{className:"bg-green-500 hover:bg-green-600 text-white border-0",children:"Activated"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(a.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Amount"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(29.99)})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Order Date"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:(0,l.GP)(new Date("2024-01-15T10:30:00Z"),"PPP")})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"w-4 h-4 text-gray-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Paid Date"}),(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:(0,l.GP)(new Date("2024-01-15T10:35:00Z"),"PPP")})]})]})]}),(0,s.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-green-600 dark:text-green-400",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Order Activated - GitHub Access Granted"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:"GitHub repository access has been granted. Check your GitHub notifications for the invitation."})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100",children:"How GitHub Access Works"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-blue-800 dark:text-blue-200",children:[(0,s.jsxs)("p",{children:["1. ",(0,s.jsx)("strong",{children:"Click Activate Order:"}),' Click the "Activate Order" button for paid orders']}),(0,s.jsxs)("p",{children:["2. ",(0,s.jsx)("strong",{children:"GitHub Invitation Modal:"})," A modal will automatically open asking for your GitHub username"]}),(0,s.jsxs)("p",{children:["3. ",(0,s.jsx)("strong",{children:"Enter Username:"}),' Provide your GitHub username (e.g., "octocat")']}),(0,s.jsxs)("p",{children:["4. ",(0,s.jsx)("strong",{children:"Auto-redirect:"})," After sending the invitation, you'll be redirected to GitHub notifications"]}),(0,s.jsxs)("p",{children:["5. ",(0,s.jsx)("strong",{children:"Accept Invitation:"})," Check your GitHub notifications and accept the repository invitation"]}),(0,s.jsxs)("p",{children:["6. ",(0,s.jsx)("strong",{children:"Access Repository:"})," Start accessing the private repository content"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Note:"})," GitHub invitations are only sent during the activation process, not for already activated orders."]})]})]}),(0,s.jsxs)("div",{className:"mt-6 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"Technical Implementation"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-gray-700 dark:text-gray-300",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"API Endpoint:"})," ",(0,s.jsx)("code",{className:"bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded",children:"POST /api/github/invite"})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"GitHub API:"})," Uses GitHub's Collaborators API to send repository invitations"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Permission Level:"}),' "pull" access (read-only) to the repository']}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Repository:"})," ShipSaaSCo/shipsaas-starter (private repository)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Authentication:"})," Requires valid session and activated order"]})]})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32915:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(91164).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},33873:e=>{"use strict";e.exports=require("path")},40727:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(21393),i=r(62019);function a(){return(0,s.jsx)(i.GitHubInviteDemo,{})}},49882:(e,t,r)=>{Promise.resolve().then(r.bind(r,23921))},57906:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=r(16507),i=r(62420),a=r(66550),n=r.n(a),o=r(99937),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["[locale]",{children:["demo",{children:["github-invite",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40727)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/demo/github-invite/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10746)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,77858)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/demo/github-invite/page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/demo/github-invite/page",pathname:"/[locale]/demo/github-invite",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},59241:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(91164).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},62019:(e,t,r)=>{"use strict";r.d(t,{GitHubInviteDemo:()=>s});let s=(0,r(57479).registerClientReference)(function(){throw Error("Attempted to call GitHubInviteDemo() from the server but GitHubInviteDemo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/components/demo/GitHubInviteDemo.tsx","GitHubInviteDemo")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68739:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(91164).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[883,331,848,540,432,268,585],()=>r(57906));module.exports=s})();