"use strict";(()=>{var e={};e.id=928,e.ids=[928],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},81831:(e,s,r)=>{r.r(s),r.d(s,{default:()=>m});var a=r(21393),t=r(53764),n=r(24750);let i=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let l=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent",t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter";var x=r(85717);function m(){return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"性能监控仪表板"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:"监控应用的核心网页指标和性能数据"})]}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:(0,a.jsx)(t.Suspense,{fallback:(0,a.jsx)(u,{}),children:(0,a.jsx)(p,{})})}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsxs)(i,{children:[(0,a.jsxs)(l,{children:[(0,a.jsx)(d,{children:"性能优化建议"}),(0,a.jsx)(o,{children:"基于当前指标的优化建议"})]}),(0,a.jsx)(c,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-green-600",children:"✅ 已优化"}),(0,a.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-muted-foreground",children:[(0,a.jsx)("li",{children:"• 使用 Next.js Image 组件进行图片优化"}),(0,a.jsx)("li",{children:"• 启用了 Gzip 压缩"}),(0,a.jsx)("li",{children:"• 配置了适当的缓存策略"}),(0,a.jsx)("li",{children:"• 使用了 Server Components 减少客户端 JS"})]})]}),(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-yellow-600",children:"⚠️ 可以改进"}),(0,a.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-muted-foreground",children:[(0,a.jsx)("li",{children:"• 考虑启用 PPR (Partial Prerendering)"}),(0,a.jsx)("li",{children:"• 添加更多的 Suspense 边界"}),(0,a.jsx)("li",{children:"• 优化第三方脚本加载"}),(0,a.jsx)("li",{children:"• 实施更细粒度的代码分割"})]})]})]})})]})})]})}function p(){return(0,a.jsx)(a.Fragment,{children:[{name:"LCP",value:"1.2s",status:"good",description:"Largest Contentful Paint",target:"< 2.5s"},{name:"FID",value:"45ms",status:"good",description:"First Input Delay",target:"< 100ms"},{name:"CLS",value:"0.08",status:"good",description:"Cumulative Layout Shift",target:"< 0.1"},{name:"FCP",value:"0.9s",status:"good",description:"First Contentful Paint",target:"< 1.8s"},{name:"TTFB",value:"120ms",status:"good",description:"Time to First Byte",target:"< 600ms"},{name:"INP",value:"85ms",status:"good",description:"Interaction to Next Paint",target:"< 200ms"}].map(e=>(0,a.jsxs)(i,{children:[(0,a.jsxs)(l,{className:"pb-2",children:[(0,a.jsx)(d,{className:"text-sm font-medium",children:e.name}),(0,a.jsx)(o,{className:"text-xs",children:e.description})]}),(0,a.jsxs)(c,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.value}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["目标: ",e.target]}),(0,a.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${"good"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"good"===e.status?"良好":"需改进"})]})]})]},e.name))})}function u(){return(0,a.jsx)(a.Fragment,{children:[1,2,3,4,5,6].map(e=>(0,a.jsxs)(i,{children:[(0,a.jsxs)(l,{className:"pb-2",children:[(0,a.jsx)(x.EA,{className:"h-4 w-16"}),(0,a.jsx)(x.EA,{className:"h-3 w-24"})]}),(0,a.jsxs)(c,{children:[(0,a.jsx)(x.EA,{className:"h-8 w-12 mb-2"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(x.EA,{className:"h-3 w-16"}),(0,a.jsx)(x.EA,{className:"h-5 w-12 rounded-full"})]})]})]},e))})}},89218:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=r(16507),t=r(62420),n=r(66550),i=r.n(n),l=r(99937),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o={children:["",{children:["[locale]",{children:["admin",{children:["performance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81831)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/admin/performance/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10746)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,77858)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/admin/performance/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/[locale]/admin/performance/page",pathname:"/[locale]/admin/performance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[883,331,848,540,268,585],()=>r(89218));module.exports=a})();