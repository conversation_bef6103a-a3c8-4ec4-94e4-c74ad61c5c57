(()=>{var e={};e.id=563,e.ids=[563],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16442:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>l});var t=s(16507),a=s(62420),o=s(66550),i=s.n(o),n=s(99937),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(r,d);let l={children:["",{children:["[locale]",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,92829)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/orders/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10746)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,77858)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/orders/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/orders/page",pathname:"/[locale]/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},60413:(e,r,s)=>{Promise.resolve().then(s.bind(s,70423))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65149:(e,r,s)=>{Promise.resolve().then(s.bind(s,92829))},70423:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(43147),a=s(26926),o=s(67880),i=s(40519),n=s(55802),d=s(30630),l=s(56783),c=s(53119),p=s(8243),h=s(73282);function u(){let e=(0,o.c3)("orders"),[r,s]=(0,a.useState)([]),[u,m]=(0,a.useState)(!0),[x,g]=(0,a.useState)(new Set),[f,v]=(0,a.useState)(!1),[b,j]=(0,a.useState)(null),{data:w}=(0,i.useSession)(),N=e=>{switch(e.toLowerCase()){case"paid":return"bg-green-500";case"pending":return"bg-yellow-500";case"failed":return"bg-red-500";default:return"bg-gray-500"}},y=(e,r)=>new Intl.NumberFormat("en-US",{style:"currency",currency:r||"USD"}).format(e/100),P=r=>{let s=r.toLowerCase();return e(`orderDetails.status.${s}`)},D=async t=>{try{g(e=>new Set(e).add(t));let e=await fetch("/api/orders/activate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderNo:t})});if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to activate order")}await e.json(),s(e=>e.map(e=>e.orderNo===t?{...e,status:"activated"}:e));let a=r.find(e=>e.orderNo===t);a&&(j({...a,status:"activated"}),v(!0))}catch(r){alert(e("orderDetails.activationError"))}finally{g(e=>{let r=new Set(e);return r.delete(t),r})}},k=e=>"paid"===e.status.toLowerCase()&&e.paidAt,_=e=>x.has(e);return u?(0,t.jsx)("div",{className:"container mx-auto py-8",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded"},e))})]})}):(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("title")}),(0,t.jsx)("p",{className:"text-gray-600 mb-8",children:e("description")}),0===r.length?(0,t.jsx)("p",{className:"text-center text-gray-500",children:e("noOrders")}):(0,t.jsx)("div",{className:"grid gap-4",children:r.map(r=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:r.productName||e("orderDetails.purchase")}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[e("orderDetails.orderId"),": ",r.orderNo]})]}),(0,t.jsx)(d.E,{className:N(r.status),children:P(r.status)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-500",children:e("orderDetails.amount")}),(0,t.jsx)("p",{className:"font-medium",children:y(r.amount,r.currency)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-500",children:e("orderDetails.orderDate")}),(0,t.jsx)("p",{className:"font-medium",children:(0,n.GP)(new Date(r.createdAt),"PPP")})]}),r.paidAt&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-500",children:e("orderDetails.paidDate")}),(0,t.jsx)("p",{className:"font-medium",children:(0,n.GP)(new Date(r.paidAt),"PPP")})]})]}),k(r)&&(0,t.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-green-500"}),(0,t.jsxs)("span",{children:[e("orderDetails.paidDate"),": ",(0,n.GP)(new Date(r.paidAt),"PPP")]})]}),(0,t.jsx)(l.$,{onClick:()=>D(r.orderNo),disabled:_(r.orderNo),className:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:_(r.orderNo)?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"w-4 h-4 mr-2 animate-spin"}),e("orderDetails.activating")]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),e("orderDetails.activateOrder")]})})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:e("orderDetails.activationDescription")})]}),"activated"===r.status.toLowerCase()&&(0,t.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-green-600 dark:text-green-400",children:[(0,t.jsx)(c.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"font-medium",children:e("orderDetails.activated")})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:e("orderDetails.activatedDescription")})]})]},r.orderNo))}),b&&(0,t.jsx)(h.a,{isOpen:f,onClose:()=>{v(!1),j(null)},orderNo:b.orderNo,productName:b.productName||e("orderDetails.purchase")})]})}},92829:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(57479).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/orders/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/orders/page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[883,331,848,540,432,268,585,282],()=>s(16442));module.exports=t})();