(()=>{var e={};e.id=943,e.ids=[943],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4294:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},10654:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:a,objectFit:n}=e,o=i?40*i:t,l=s?40*s:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},10807:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let i=r(27411),s=r(63119),a=r(77055),n=i._(r(68599));function o(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=a.Image},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21233:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var i=r(21393),s=r(35003),a=r(70721),n=r(95842),o=r(64064),l=r(82578),d=r(10807),u=r.n(d),c=r(54300);async function m({params:e}){let{locale:t}=await e;(0,c.I)(t);let r=await (0,l.A)("profile"),d=await (0,s.getServerSession)(a.$);if(!d?.user?.email)return(0,o.notFound)();let m=await n.zR.user.findFirst({where:{email:d.user.email}});return m?(0,i.jsx)("div",{className:"py-10",children:(0,i.jsxs)("div",{className:"mx-auto max-w-3xl bg-card text-card-foreground shadow overflow-hidden sm:rounded-lg border border-border",children:[(0,i.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold leading-6 text-foreground",children:r("title")}),(0,i.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-muted-foreground",children:r("subtitle")})]}),(0,i.jsx)("div",{className:"border-t border-border px-4 py-5 sm:px-6",children:(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:m.avatarUrl?(0,i.jsx)(u(),{className:"h-24 w-24 rounded-full",src:m.avatarUrl,alt:m.nickname||"",width:96,height:96,unoptimized:!0}):(0,i.jsx)("div",{className:"h-24 w-24 rounded-full bg-muted flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-3xl text-muted-foreground",children:m.nickname?.charAt(0)||m.email.charAt(0).toUpperCase()})})}),(0,i.jsxs)("div",{className:"ml-6",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold leading-tight text-foreground",children:m.nickname||r("unknown")}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:m.email})]})]})}),(0,i.jsx)("div",{className:"border-t border-border",children:(0,i.jsxs)("dl",{children:[(0,i.jsxs)("div",{className:"bg-muted px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-muted-foreground",children:r("uuid")}),(0,i.jsx)("dd",{className:"mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0",children:m.uuid})]}),(0,i.jsxs)("div",{className:"bg-card px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-muted-foreground",children:r("email")}),(0,i.jsx)("dd",{className:"mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0",children:m.email})]}),(0,i.jsxs)("div",{className:"bg-muted px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-muted-foreground",children:r("nickname")}),(0,i.jsx)("dd",{className:"mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0",children:m.nickname||r("unknown")})]}),(0,i.jsxs)("div",{className:"bg-card px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-muted-foreground",children:r("registrationDate")}),(0,i.jsx)("dd",{className:"mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0",children:new Date(m.createdAt).toLocaleString()})]}),(0,i.jsxs)("div",{className:"bg-muted px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-muted-foreground",children:r("signinMethod")}),(0,i.jsx)("dd",{className:"mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0",children:m.signinProvider?.toUpperCase()||r("unknown")})]}),(0,i.jsxs)("div",{className:"bg-card px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-muted-foreground",children:r("lastSigninIp")}),(0,i.jsx)("dd",{className:"mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0",children:m.signinIp||r("unknown")})]})]})})]})}):(0,o.notFound)()}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},53394:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58245:(e,t,r)=>{Promise.resolve().then(r.bind(r,14239)),Promise.resolve().then(r.t.bind(r,77055,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63119:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(4294);let i=r(10654),s=r(53394),a=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,u,c,{src:m,sizes:p,unoptimized:g=!1,priority:h=!1,loading:f,className:x,quality:v,width:b,height:w,fill:y=!1,style:_,overrideSrc:j,onLoad:P,onLoadingComplete:E,placeholder:O="empty",blurDataURL:N,fetchPriority:k,decoding:S="async",layout:A,objectFit:R,objectPosition:U,lazyBoundary:C,lazyRoot:z,...I}=e,{imgConf:T,showAltText:q,blurComplete:D,defaultLoader:G}=t,M=T||s.imageConfigDefault;if("allSizes"in M)d=M;else{let e=[...M.deviceSizes,...M.imageSizes].sort((e,t)=>e-t),t=M.deviceSizes.sort((e,t)=>e-t),i=null==(r=M.qualities)?void 0:r.sort((e,t)=>e-t);d={...M,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=I.loader||G;delete I.loader,delete I.srcSet;let H="__next_img_default"in F;if(H){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...i}=t;return e(i)}}if(A){"fill"===A&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[A];e&&(_={..._,...e});let t={responsive:"100vw",fill:"100vw"}[A];t&&!p&&(p=t)}let L="",B=o(b),W=o(w);if((l=m)&&"object"==typeof l&&(n(l)||void 0!==l.src)){let e=n(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(u=e.blurWidth,c=e.blurHeight,N=N||e.blurDataURL,L=e.src,!y)if(B||W){if(B&&!W){let t=B/e.width;W=Math.round(e.height*t)}else if(!B&&W){let t=W/e.height;B=Math.round(e.width*t)}}else B=e.width,W=e.height}let V=!h&&("lazy"===f||void 0===f);(!(m="string"==typeof m?m:L)||m.startsWith("data:")||m.startsWith("blob:"))&&(g=!0,V=!1),d.unoptimized&&(g=!0),H&&!d.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(g=!0);let X=o(v),J=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:R,objectPosition:U}:{},q?{}:{color:"transparent"},_),$=D||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:B,heightInt:W,blurWidth:u,blurHeight:c,blurDataURL:N||"",objectFit:J.objectFit})+'")':'url("'+O+'")',Y=a.includes(J.objectFit)?"fill"===J.objectFit?"100% 100%":"cover":J.objectFit,K=$?{backgroundSize:Y,backgroundPosition:J.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},Q=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:a,sizes:n,loader:o}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,n),u=l.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:l.map((e,i)=>o({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:o({config:t,src:r,quality:a,width:l[u]})}}({config:d,src:m,unoptimized:g,width:B,quality:X,sizes:p,loader:F});return{props:{...I,loading:V?"lazy":f,fetchPriority:k,width:B,height:W,decoding:S,className:x,style:{...J,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:j||Q.src},meta:{unoptimized:g,priority:h,placeholder:O,fill:y}}}},68599:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:a}=e,n=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+n+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},70721:(e,t,r)=>{"use strict";r.d(t,{$:()=>p});var i=r(83761),s=r(53829),a=r(95842),n=r(32967),o=r(20331),l=r(75758),d=r(17773),u=r(97641);r(35003);let c=(0,l.A)({id:"google-one-tap",name:"Google One Tap",credentials:{credential:{type:"text"}},async authorize(e){try{if(!e?.credential)return null;let t=(0,d.s)(e.credential),r={uuid:t.sub,email:t.email,nickname:t.name,avatarUrl:t.picture,signinType:"oauth",signinIp:"127.0.0.1",signinProvider:"google-one-tap",signinOpenid:t.sub,createdAt:new Date},i=await a.zR.user.findFirst({where:{email:t.email,signinProvider:"google-one-tap"}});return i?await a.zR.user.update({where:{id:i.id},data:{nickname:r.nickname,avatarUrl:r.avatarUrl}}):await a.zR.user.create({data:r}),{id:t.sub,email:t.email,name:t.name,image:t.picture,uuid:t.sub}}catch(e){return null}}}),m=(0,l.A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await (0,a.iA)(e.email,"credentials");return t&&t.password&&await (0,u.UD)(e.password,t.password)?{id:t.id.toString(),email:t.email,name:t.nickname||"",image:t.avatarUrl||"",uuid:t.uuid}:null}}),p={providers:[...process.env.AUTH_GITHUB_ID&&process.env.AUTH_GITHUB_SECRET?[(0,s.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET})]:[],...process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET?[(0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET})]:[],c,m],debug:!1,secret:process.env.AUTH_SECRET,cookies:{sessionToken:{name:"next-auth.session-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},callbackUrl:{name:"next-auth.callback-url",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},csrfToken:{name:"next-auth.csrf-token",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0}},pkceCodeVerifier:{name:"next-auth.pkce.code_verifier",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}},state:{name:"next-auth.state",options:{httpOnly:!0,sameSite:"lax",path:"/",secure:!0,maxAge:900}}},callbacks:{async jwt({token:e,user:t,account:r}){if(t&&(e.id=t.id,"uuid"in t&&(e.uuid=t.uuid)),t&&r&&"credentials"!==r.provider)try{let i=await a.zR.user.findFirst({where:{email:t.email||"",signinProvider:r.provider}});i||(i=await a.zR.user.create({data:{uuid:(0,n.A)(),email:t.email||"",nickname:t.name||"",avatarUrl:t.image||"",signinProvider:r.provider,signinOpenid:r.providerAccountId}})),e.id=i.id.toString(),e.uuid=i.uuid}catch(e){}return e},session:async({session:e,token:t})=>(e.user&&t&&(e.user.id=t.id,e.user.uuid=t.uuid),e),async signIn({user:e,account:t,profile:r}){try{if(!e.email)return!1;let i="127.0.0.1";try{let e=(await (0,o.headers)()).get("x-forwarded-for");i=e?e.split(",")[0]:"127.0.0.1"}catch(e){}let s={uuid:(0,n.A)(),email:e.email,nickname:r?.name||e.name,avatarUrl:r?.picture||r?.avatar_url||e.image,signinType:"oauth",signinIp:i,signinProvider:t?.provider,signinOpenid:t?.providerAccountId,createdAt:new Date},l=await a.zR.user.findFirst({where:{email:e.email,signinProvider:t?.provider}});return l?await a.zR.user.update({where:{id:l.id},data:{nickname:s.nickname,avatarUrl:s.avatarUrl,signinIp:i}}):await a.zR.user.create({data:s}),!0}catch(e){return e instanceof Error,!0}}},pages:{signIn:"/auth/signin",error:"/auth/signin"},session:{strategy:"jwt",maxAge:2592e3}}},74075:e=>{"use strict";e.exports=require("zlib")},76086:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var i=r(16507),s=r(62420),a=r(66550),n=r.n(a),o=r(99937),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21233)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,10746)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,77858)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/loading.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/profile/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/profile/page",pathname:"/[locale]/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77055:(e,t,r)=>{let{createProxy:i}=r(19480);e.exports=i("/Users/<USER>/fuwenhao/github/shipsaas-office/node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/image-component.js")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82578:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(53764),s=r(63868),a=r(61286),n=(0,i.cache)(function(e,t){return(0,a.HM)({...e,namespace:t})}),o=(0,i.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),n(await (0,s.A)(r),t)})},87749:(e,t,r)=>{Promise.resolve().then(r.bind(r,8081)),Promise.resolve().then(r.t.bind(r,72961,23))},94735:e=>{"use strict";e.exports=require("events")},95842:(e,t,r)=>{"use strict";r.d(t,{Jn:()=>n,iA:()=>a,zR:()=>s});let i=require("@prisma/client"),s=globalThis.prisma??new i.PrismaClient;async function a(e,t){return await s.user.findFirst({where:{email:e,signinProvider:t,isDeleted:!1}})}async function n(e){return await s.user.create({data:{uuid:e.uuid,email:e.email,password:e.password,signinProvider:e.signinProvider,nickname:e.nickname,isDeleted:!1}})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[883,331,848,540,47,690,268,585],()=>r(76086));module.exports=i})();