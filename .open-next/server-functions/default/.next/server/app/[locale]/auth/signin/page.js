(()=>{var e={};e.id=867,e.ids=[867],e.modules={999:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(21393),i=t(53764),a=t(82578),n=t(54300),o=t(31426);async function l(e){let s=await e.searchParams,{locale:t}=await e.params;(0,n.I)(t);let l=await (0,a.A)("auth"),d=s?.error,c=s?.callbackUrl||"/",u=s?.email||"",h="";if(d)switch(d){case"OAuthCallback":h=l("oauthError");break;case"CredentialsSignin":h=l("invalidCredentials");break;default:h=l("authError")}return(0,r.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900",children:l("signInTitle")}),h&&(0,r.jsx)("div",{className:"mt-4 p-4 text-sm text-red-600 bg-red-50 rounded-md",children:h})]}),(0,r.jsx)(i.Suspense,{fallback:(0,r.jsx)("div",{className:"text-center",children:"Loading..."}),children:(0,r.jsx)(o.default,{callbackUrl:c,initialEmail:u})})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11557:(e,s,t)=>{Promise.resolve().then(t.bind(t,14239)),Promise.resolve().then(t.bind(t,31426))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24709:(e,s,t)=>{Promise.resolve().then(t.bind(t,8081)),Promise.resolve().then(t.bind(t,44012))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30233:(e,s,t)=>{Promise.resolve().then(t.bind(t,14239))},31426:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(57479).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/auth/signin/SignInForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/auth/signin/SignInForm.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},44012:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var r=t(43147),i=t(40519),a=t(26926),n=t(67880),o=t(45729);let l=()=>(0,r.jsxs)("svg",{className:"h-5 w-5",width:"20",height:"20",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,r.jsx)("title",{children:"Google Logo"}),(0,r.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,r.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,r.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,r.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),d=()=>(0,r.jsxs)("svg",{className:"h-5 w-5",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,r.jsx)("title",{children:"GitHub Logo"}),(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 2C6.477 2 2 6.477 2 12c0 4.42 2.87 8.17 6.84 ********.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34-.46-1.16-1.11-1.47-1.11-1.47-.91-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.87 1.52 2.34 1.07 **********-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.92 0-1.11.38-2 1.03-2.71-.1-.25-.45-1.29.1-2.64 0 0 .84-.27 2.75 1.02.79-.22 1.65-.33 2.5-.33.85 0 1.71.11 2.5.33 1.91-1.29 2.75-1.02 2.75-1.02.55 1.35.2 2.39.1 ********** 1.03 1.6 1.03 2.71 0 3.82-2.34 4.66-4.57 **********.69.92.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0012 2z"})]});function c({callbackUrl:e,initialEmail:s=""}){let[t,c]=(0,a.useState)(""),[u,h]=(0,a.useState)(s),[m,p]=(0,a.useState)(""),[g,f]=(0,a.useState)(""),[x,b]=(0,a.useState)(!1),[w,v]=(0,a.useState)(""),[y,j]=(0,a.useState)(""),N=(0,n.c3)("auth");(0,o.useRouter)();let C=async s=>{try{c(s),f("");let t=await (0,i.signIn)(s,{callbackUrl:e||"/",redirect:!1});t?.error?f(t.error):t?.url&&(window.location.href=t.url)}catch(e){f(N("signInError"))}finally{c("")}},k=async s=>{s.preventDefault();try{f(""),c("credentials");let s=await (0,i.signIn)("credentials",{email:u,password:m,callbackUrl:e||"/",redirect:!1});s?.error?f(N("invalidCredentials")):s?.url&&(window.location.href=s.url)}catch(e){f(N("signInError"))}finally{c("")}},P=async e=>{if(e.preventDefault(),m!==w)return void f(N("passwordsDoNotMatch"));try{f(""),j(""),c("signup");let s=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:u,password:m})}),t=await s.json();if(!s.ok)throw Error(t.message||N("signUpError"));j(N("signUpSuccess")),b(!1),setTimeout(()=>{k(e)},1e3)}catch(e){f(e instanceof Error?e.message:N("signUpError"))}finally{c("")}},I=()=>{b(!x),f(""),j("")};return(0,r.jsxs)("div",{className:"mt-8 space-y-6",children:[y&&(0,r.jsx)("div",{className:"p-4 text-sm text-green-700 bg-green-100 rounded-md",children:y}),x?(0,r.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:N("email")}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:u,onChange:e=>h(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",disabled:!!t})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:N("password")}),(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:m,onChange:e=>p(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",disabled:!!t})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:N("confirmPassword")}),(0,r.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:w,onChange:e=>v(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",disabled:!!t})]}),g&&(0,r.jsx)("div",{className:"text-red-500 text-sm",children:g}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:!!t,className:"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"signup"===t?N("signingUp"):N("signUp")})}),(0,r.jsx)("div",{className:"text-sm text-center",children:(0,r.jsx)("button",{type:"button",onClick:I,className:"font-medium text-indigo-600 hover:text-indigo-500",children:N("alreadyHaveAccount")})})]}):(0,r.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:N("email")}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:u,onChange:e=>h(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",disabled:!!t})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:N("password")}),(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:m,onChange:e=>p(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",disabled:!!t})]}),g&&(0,r.jsx)("div",{className:"text-red-500 text-sm",children:g}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:!!t,className:"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"credentials"===t?N("signingIn"):N("signIn")})}),(0,r.jsx)("div",{className:"text-sm text-center",children:(0,r.jsx)("button",{type:"button",onClick:I,className:"font-medium text-indigo-600 hover:text-indigo-500",children:N("noAccount")})})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:N("orContinueWith")})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>C("google"),disabled:!!t,className:"w-full flex items-center justify-center gap-3 py-2.5 px-4 border border-gray-300 rounded-lg text-sm font-medium bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4285F4] disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(l,{}),"google"===t?N("signingIn"):N("signInWithGoogle")]}),(0,r.jsxs)("button",{type:"button",onClick:()=>C("github"),disabled:!!t,className:"w-full flex items-center justify-center gap-3 py-2.5 px-4 border border-gray-300 rounded-lg text-sm font-medium bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#24292F] disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(d,{}),"github"===t?N("signingIn"):N("signInWithGithub")]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66681:(e,s,t)=>{Promise.resolve().then(t.bind(t,8081))},80514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=t(16507),i=t(62420),a=t(66550),n=t.n(a),o=t(99937),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["[locale]",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,999)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/auth/signin/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,90529)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/auth/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,10746)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,77858)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,8143)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,73693)),"/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,72307,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,37896,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/fuwenhao/github/shipsaas-office/src/app/[locale]/auth/signin/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/auth/signin/page",pathname:"/[locale]/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},82578:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(53764),i=t(63868),a=t(61286),n=(0,r.cache)(function(e,s){return(0,a.HM)({...e,namespace:s})}),o=(0,r.cache)(async function(e){let s,t;return"string"==typeof e?s=e:e&&(t=e.locale,s=e.namespace),n(await (0,i.A)(t),s)})},90529:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,generateMetadata:()=>l});var r=t(21393),i=t(54300),a=t(82578),n=t(99086),o=t(64064);async function l({params:e}){try{let{locale:s}=await e;if(!n.DT.locales.includes(s))throw Error(`Invalid locale: ${s}`);let t=await (0,a.A)("auth");return{title:t("signInTitle"),description:t("signInDescription"),robots:{index:!1,follow:!1}}}catch(e){return{title:"Authentication",description:"User authentication",robots:{index:!1,follow:!1}}}}async function d({children:e,params:s}){try{let{locale:t}=await s;return n.DT.locales.includes(t)||(0,o.notFound)(),(0,i.I)(t),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"flex min-h-screen flex-col justify-center",children:e})})}catch(e){(0,o.notFound)()}}}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[883,331,848,540,268,585],()=>t(80514));module.exports=r})();