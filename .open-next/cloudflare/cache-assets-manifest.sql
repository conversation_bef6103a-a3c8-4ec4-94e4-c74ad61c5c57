CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/layout", "7-6uNB1ybMvQxKKPPRxSu/_not-found"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/_not-found/layout", "7-6uNB1ybMvQxKKPPRxSu/_not-found"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/_not-found/page", "7-6uNB1ybMvQxKKPPRxSu/_not-found"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/_not-found", "7-6uNB1ybMvQxKKPPRxSu/_not-found"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/layout", "7-6uNB1ybMvQxKKPPRxSu/en"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/[locale]/layout", "7-6uNB1ybMvQxKKPPRxSu/en"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/[locale]/page", "7-6uNB1ybMvQxKKPPRxSu/en"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/en", "7-6uNB1ybMvQxKKPPRxSu/en"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/layout", "7-6uNB1ybMvQxKKPPRxSu/robots.txt"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/robots.txt/layout", "7-6uNB1ybMvQxKKPPRxSu/robots.txt"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/robots.txt/route", "7-6uNB1ybMvQxKKPPRxSu/robots.txt"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/robots.txt", "7-6uNB1ybMvQxKKPPRxSu/robots.txt"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/layout", "7-6uNB1ybMvQxKKPPRxSu/sitemap.xml"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/sitemap.xml/layout", "7-6uNB1ybMvQxKKPPRxSu/sitemap.xml"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/sitemap.xml/route", "7-6uNB1ybMvQxKKPPRxSu/sitemap.xml"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/sitemap.xml", "7-6uNB1ybMvQxKKPPRxSu/sitemap.xml"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/layout", "7-6uNB1ybMvQxKKPPRxSu/zh"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/[locale]/layout", "7-6uNB1ybMvQxKKPPRxSu/zh"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/[locale]/page", "7-6uNB1ybMvQxKKPPRxSu/zh"), ("7-6uNB1ybMvQxKKPPRxSu/_N_T_/zh", "7-6uNB1ybMvQxKKPPRxSu/zh");