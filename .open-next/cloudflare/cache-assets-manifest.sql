CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("fxHrLFl77V256rk_XJYsj/_N_T_/layout", "fxHrLFl77V256rk_XJYsj/_not-found"), ("fxHrLFl77V256rk_XJYsj/_N_T_/_not-found/layout", "fxHrLFl77V256rk_XJYsj/_not-found"), ("fxHrLFl77V256rk_XJYsj/_N_T_/_not-found/page", "fxHrLFl77V256rk_XJYsj/_not-found"), ("fxHrLFl77V256rk_XJYsj/_N_T_/_not-found", "fxHrLFl77V256rk_XJYsj/_not-found"), ("fxHrLFl77V256rk_XJYsj/_N_T_/layout", "fxHrLFl77V256rk_XJYsj/en"), ("fxHrLFl77V256rk_XJYsj/_N_T_/[locale]/layout", "fxHrLFl77V256rk_XJYsj/en"), ("fxHrLFl77V256rk_XJYsj/_N_T_/[locale]/page", "fxHrLFl77V256rk_XJYsj/en"), ("fxHrLFl77V256rk_XJYsj/_N_T_/en", "fxHrLFl77V256rk_XJYsj/en"), ("fxHrLFl77V256rk_XJYsj/_N_T_/layout", "fxHrLFl77V256rk_XJYsj/robots.txt"), ("fxHrLFl77V256rk_XJYsj/_N_T_/robots.txt/layout", "fxHrLFl77V256rk_XJYsj/robots.txt"), ("fxHrLFl77V256rk_XJYsj/_N_T_/robots.txt/route", "fxHrLFl77V256rk_XJYsj/robots.txt"), ("fxHrLFl77V256rk_XJYsj/_N_T_/robots.txt", "fxHrLFl77V256rk_XJYsj/robots.txt"), ("fxHrLFl77V256rk_XJYsj/_N_T_/layout", "fxHrLFl77V256rk_XJYsj/sitemap.xml"), ("fxHrLFl77V256rk_XJYsj/_N_T_/sitemap.xml/layout", "fxHrLFl77V256rk_XJYsj/sitemap.xml"), ("fxHrLFl77V256rk_XJYsj/_N_T_/sitemap.xml/route", "fxHrLFl77V256rk_XJYsj/sitemap.xml"), ("fxHrLFl77V256rk_XJYsj/_N_T_/sitemap.xml", "fxHrLFl77V256rk_XJYsj/sitemap.xml"), ("fxHrLFl77V256rk_XJYsj/_N_T_/layout", "fxHrLFl77V256rk_XJYsj/zh"), ("fxHrLFl77V256rk_XJYsj/_N_T_/[locale]/layout", "fxHrLFl77V256rk_XJYsj/zh"), ("fxHrLFl77V256rk_XJYsj/_N_T_/[locale]/page", "fxHrLFl77V256rk_XJYsj/zh"), ("fxHrLFl77V256rk_XJYsj/_N_T_/zh", "fxHrLFl77V256rk_XJYsj/zh");