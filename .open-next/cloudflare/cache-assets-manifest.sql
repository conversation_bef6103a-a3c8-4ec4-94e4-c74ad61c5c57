CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/layout", "MVxtIqnVSLH5ZB7VfVV84/_not-found"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/_not-found/layout", "MVxtIqnVSLH5ZB7VfVV84/_not-found"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/_not-found/page", "MVxtIqnVSLH5ZB7VfVV84/_not-found"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/_not-found", "MVxtIqnVSLH5ZB7VfVV84/_not-found"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/layout", "MVxtIqnVSLH5ZB7VfVV84/en"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/[locale]/layout", "MVxtIqnVSLH5ZB7VfVV84/en"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/[locale]/page", "MVxtIqnVSLH5ZB7VfVV84/en"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/en", "MVxtIqnVSLH5ZB7VfVV84/en"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/layout", "MVxtIqnVSLH5ZB7VfVV84/robots.txt"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/robots.txt/layout", "MVxtIqnVSLH5ZB7VfVV84/robots.txt"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/robots.txt/route", "MVxtIqnVSLH5ZB7VfVV84/robots.txt"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/robots.txt", "MVxtIqnVSLH5ZB7VfVV84/robots.txt"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/layout", "MVxtIqnVSLH5ZB7VfVV84/sitemap.xml"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/sitemap.xml/layout", "MVxtIqnVSLH5ZB7VfVV84/sitemap.xml"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/sitemap.xml/route", "MVxtIqnVSLH5ZB7VfVV84/sitemap.xml"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/sitemap.xml", "MVxtIqnVSLH5ZB7VfVV84/sitemap.xml"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/layout", "MVxtIqnVSLH5ZB7VfVV84/zh"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/[locale]/layout", "MVxtIqnVSLH5ZB7VfVV84/zh"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/[locale]/page", "MVxtIqnVSLH5ZB7VfVV84/zh"), ("MVxtIqnVSLH5ZB7VfVV84/_N_T_/zh", "MVxtIqnVSLH5ZB7VfVV84/zh");