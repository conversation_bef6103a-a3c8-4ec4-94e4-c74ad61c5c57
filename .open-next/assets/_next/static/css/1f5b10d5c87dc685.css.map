{"version": 3, "sources": ["webpack://_N_E/node_modules/.pnpm/next@15.3.5_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/%3Cinput%20css%200snNkK%3E", "webpack://_N_E/<no source>", "webpack://_N_E/1f5b10d5c87dc685.css", "webpack://_N_E/src/app/theme.css"], "names": [], "mappings": "AACA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,+DACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,oBACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,0JACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gMACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,uEAAwE,CACxE,iKACF,CC9DA,WAAA,0BAAA,CAAA,kBAAA,CAAA,sBAAA,CAAA,uBAAA,CAAA,uBAAA,CAAA,mBC+DA,CD/DA,oBAAA,gCAAA,CAAA,iBCgEA,CDhEA,mBAAA,qCCiEA,CCjEA,iBAAA,uBAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,mCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,WAAA,uBAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,mCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,iBAAA,qBAAc,CAAd,sBAAc,CAAd,eAAA,eAAc,CAAd,WAAA,eAAc,CAAd,6BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,gHAAc,CAAd,4BAAc,CAAd,8BAAc,CAAd,uCAAc,CAAd,KAAA,QAAc,CAAd,mBAAc,CAAd,GAAA,QAAc,CAAd,aAAc,CAAd,oBAAc,CAAd,oBAAA,gCAAc,CAAd,kBAAA,iBAAc,CAAd,mBAAc,CAAd,EAAA,aAAc,CAAd,uBAAc,CAAd,SAAA,kBAAc,CAAd,kBAAA,mGAAc,CAAd,4BAAc,CAAd,8BAAc,CAAd,aAAc,CAAd,MAAA,aAAc,CAAd,QAAA,aAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,uBAAc,CAAd,IAAA,aAAc,CAAd,IAAA,SAAc,CAAd,MAAA,aAAc,CAAd,oBAAc,CAAd,wBAAc,CAAd,sCAAA,mBAAc,CAAd,6BAAc,CAAd,+BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,SAAc,CAAd,cAAA,mBAAc,CAAd,uFAAA,yBAAc,CAAd,4BAAc,CAAd,qBAAc,CAAd,gBAAA,YAAc,CAAd,iBAAA,eAAc,CAAd,SAAA,uBAAc,CAAd,wDAAA,WAAc,CAAd,cAAA,4BAAc,CAAd,mBAAc,CAAd,4BAAA,uBAAc,CAAd,6BAAA,yBAAc,CAAd,YAAc,CAAd,QAAA,iBAAc,CAAd,mDAAA,QAAc,CAAd,SAAA,QAAc,CAAd,gBAAA,SAAc,CAAd,WAAA,eAAc,CAAd,QAAc,CAAd,SAAc,CAAd,OAAA,SAAc,CAAd,SAAA,eAAc,CAAd,yCAAA,SAAc,CAAd,aAAc,CAAd,qBAAA,cAAc,CAAd,UAAA,cAAc,CAAd,+CAAA,aAAc,CAAd,qBAAc,CAAd,UAAA,cAAc,CAAd,WAAc,CAAd,2CAAA,YAAc,CAAd,KAAA,uCAAc,CAAd,4BAAc,CACd,WAAA,UAAoB,CAApB,yBAAA,WAAA,eAAoB,CAAA,CAApB,yBAAA,WAAA,eAAoB,CAAA,CAApB,0BAAA,WAAA,gBAAoB,CAAA,CAApB,0BAAA,WAAA,gBAAoB,CAAA,CAApB,0BAAA,WAAA,gBAAoB,CAAA,CAwEhB,OAAA,mBAA6K,CAA7K,kBAA6K,CAA7K,oBAA6K,CAA7K,gBAA6K,CAA7K,uBAA6K,CAA7K,gBAA6K,CAA7K,gBAA6K,CAA7K,eAA6K,CAA7K,yFAA6K,CAA7K,kDAA6K,CAA7K,wBAA6K,CAA7K,aAAA,6BAA6K,CAA7K,kBAA6K,CAA7K,0GAA6K,CAA7K,wGAA6K,CAA7K,wFAA6K,CAA7K,gCAA6K,CAA7K,0BAA6K,CAvEjL,SAAA,iBAAmB,CAAnB,SAAmB,CAAnB,UAAmB,CAAnB,SAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,cAAmB,CAAnB,qBAAA,mBAAmB,CAAnB,qBAAA,mBAAmB,CAAnB,SAAA,kBAAmB,CAAnB,OAAA,cAAmB,CAAnB,UAAA,iBAAmB,CAAnB,UAAA,iBAAmB,CAAnB,QAAA,eAAmB,CAAnB,SAAA,OAAmB,CAAnB,WAAA,MAAmB,CAAnB,OAAmB,CAAnB,WAAA,KAAmB,CAAnB,QAAmB,CAAnB,UAAA,WAAmB,CAAnB,UAAA,QAAmB,CAAnB,UAAA,WAAmB,CAAnB,QAAA,MAAmB,CAAnB,WAAA,QAAmB,CAAnB,QAAA,UAAmB,CAAnB,QAAA,WAAmB,CAAnB,QAAA,SAAmB,CAAnB,gBAAA,SAAmB,CAAnB,eAAA,QAAmB,CAAnB,SAAA,OAAmB,CAAnB,SAAA,YAAmB,CAAnB,SAAA,UAAmB,CAAnB,OAAA,KAAmB,CAAnB,OAAA,UAAmB,CAAnB,UAAA,OAAmB,CAAnB,OAAA,QAAmB,CAAnB,cAAA,OAAmB,CAAnB,OAAA,WAAmB,CAAnB,KAAA,SAAmB,CAAnB,MAAA,UAAmB,CAAnB,MAAA,UAAmB,CAAnB,MAAA,UAAmB,CAAnB,SAAA,SAAmB,CAAnB,SAAA,SAAmB,CAAnB,YAAA,yBAAmB,CAAnB,OAAA,mBAAmB,CAAnB,oBAAmB,CAAnB,SAAA,gBAAmB,CAAnB,iBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,YAAA,oBAAmB,CAAnB,OAAA,oBAAmB,CAAnB,OAAA,kBAAmB,CAAnB,OAAA,kBAAmB,CAAnB,MAAA,mBAAmB,CAAnB,MAAA,oBAAmB,CAAnB,MAAA,kBAAmB,CAAnB,MAAA,oBAAmB,CAAnB,MAAA,kBAAmB,CAAnB,MAAA,kBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,MAAA,gBAAmB,CAAnB,MAAA,kBAAmB,CAAnB,SAAA,gBAAmB,CAAnB,MAAA,kBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,SAAA,kBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,OAAA,eAAmB,CAAnB,OAAA,eAAmB,CAAnB,MAAA,gBAAmB,CAAnB,MAAA,eAAmB,CAAnB,MAAA,iBAAmB,CAAnB,MAAA,eAAmB,CAAnB,OAAA,aAAmB,CAAnB,cAAA,oBAAmB,CAAnB,MAAA,YAAmB,CAAnB,aAAA,mBAAmB,CAAnB,MAAA,YAAmB,CAAnB,QAAA,YAAmB,CAAnB,eAAA,gBAAmB,CAAnB,KAAA,aAAmB,CAAnB,MAAA,aAAmB,CAAnB,MAAA,cAAmB,CAAnB,MAAA,WAAmB,CAAnB,MAAA,WAAmB,CAAnB,KAAA,YAAmB,CAAnB,MAAA,WAAmB,CAAnB,KAAA,aAAmB,CAAnB,QAAA,cAAmB,CAAnB,MAAA,WAAmB,CAAnB,KAAA,WAAmB,CAAnB,KAAA,cAAmB,CAAnB,KAAA,aAAmB,CAAnB,KAAA,WAAmB,CAAnB,KAAA,cAAmB,CAAnB,eAAA,aAAmB,CAAnB,YAAA,WAAmB,CAAnB,QAAA,WAAmB,CAAnB,MAAA,UAAmB,CAAnB,UAAA,YAAmB,CAAnB,gBAAA,eAAmB,CAAnB,8BAAA,6BAAmB,CAAnB,cAAA,gBAAmB,CAAnB,KAAA,YAAmB,CAAnB,QAAA,gBAAmB,CAAnB,QAAA,SAAmB,CAAnB,MAAA,YAAmB,CAAnB,MAAA,aAAmB,CAAnB,MAAA,UAAmB,CAAnB,MAAA,UAAmB,CAAnB,KAAA,WAAmB,CAAnB,QAAA,gBAAmB,CAAnB,MAAA,UAAmB,CAAnB,MAAA,UAAmB,CAAnB,QAAA,aAAmB,CAAnB,QAAA,SAAmB,CAAnB,MAAA,UAAmB,CAAnB,KAAA,UAAmB,CAAnB,MAAA,WAAmB,CAAnB,KAAA,aAAmB,CAAnB,KAAA,YAAmB,CAAnB,MAAA,WAAmB,CAAnB,KAAA,UAAmB,CAAnB,KAAA,aAAmB,CAAnB,eAAA,YAAmB,CAAnB,YAAA,UAAmB,CAAnB,OAAA,iBAAmB,CAAnB,QAAA,UAAmB,CAAnB,UAAA,WAAmB,CAAnB,iBAAA,eAAmB,CAAnB,gBAAA,cAAmB,CAAnB,WAAA,eAAmB,CAAnB,WAAA,eAAmB,CAAnB,WAAA,eAAmB,CAAnB,WAAA,eAAmB,CAAnB,WAAA,eAAmB,CAAnB,WAAA,eAAmB,CAAnB,iBAAA,eAAmB,CAAnB,iBAAA,eAAmB,CAAnB,iBAAA,eAAmB,CAAnB,iBAAA,gBAAmB,CAAnB,UAAA,eAAmB,CAAnB,UAAA,eAAmB,CAAnB,UAAA,eAAmB,CAAnB,QAAA,WAAmB,CAAnB,WAAA,SAAmB,CAAnB,yBAAA,aAAmB,CAAnB,aAAA,qBAAmB,CAAnB,kBAAA,0BAAmB,CAAnB,mBAAA,qBAAmB,CAAnB,mCAAA,6LAAmB,CAAnB,gBAAA,yBAAmB,CAAnB,mBAAA,qBAAmB,CAAnB,0CAAA,6LAAmB,CAAnB,uBAAA,qBAAmB,CAAnB,gBAAA,uBAAmB,CAAnB,uCAAA,6LAAmB,CAAnB,uBAAA,qBAAmB,CAAnB,UAAA,gBAAmB,CAAnB,qBAAA,6LAAmB,CAAnB,WAAA,iBAAmB,CAAnB,SAAA,cAAmB,CAAnB,cAAmB,CAAnB,oBAAA,6LAAmB,CAAnB,WAAA,cAAmB,CAAnB,cAAmB,CAAnB,WAAA,6LAAmB,CAAnB,eAAA,iMAAmB,CAAnB,mBAAA,GAAA,uBAAmB,CAAnB,GAAA,8CAAmB,CAAA,CAAnB,iBAAA,iDAAmB,CAAnB,iBAAA,IAAA,UAAmB,CAAA,CAAnB,eAAA,mDAAmB,CAAnB,gBAAA,GAAA,uBAAmB,CAAA,CAAnB,cAAA,iCAAmB,CAAnB,gBAAA,cAAmB,CAAnB,gBAAA,cAAmB,CAAnB,aAAA,gBAAmB,CAAnB,WAAA,oBAAmB,CAAnB,qBAAA,oBAAmB,CAAnB,aAAA,6CAAmB,CAAnB,aAAA,6CAAmB,CAAnB,aAAA,6CAAmB,CAAnB,UAAA,kBAAmB,CAAnB,UAAA,qBAAmB,CAAnB,kBAAA,6BAAmB,CAAnB,WAAA,cAAmB,CAAnB,aAAA,sBAAmB,CAAnB,cAAA,kBAAmB,CAAnB,gBAAA,oBAAmB,CAAnB,gBAAA,sBAAmB,CAAnB,iBAAA,6BAAmB,CAAnB,gBAAA,4BAAmB,CAAnB,sBAAA,oBAAmB,CAAnB,OAAA,UAAmB,CAAnB,QAAA,QAAmB,CAAnB,QAAA,QAAmB,CAAnB,OAAA,SAAmB,CAAnB,UAAA,WAAmB,CAAnB,OAAA,UAAmB,CAAnB,OAAA,QAAmB,CAAnB,OAAA,UAAmB,CAAnB,OAAA,QAAmB,CAAnB,0CAAA,sBAAmB,CAAnB,qDAAmB,CAAnB,8DAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,qDAAmB,CAAnB,8DAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,qDAAmB,CAAnB,8DAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,qDAAmB,CAAnB,8DAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,6DAAmB,CAAnB,sDAAmB,CAAnB,4CAAA,sBAAmB,CAAnB,8DAAmB,CAAnB,uDAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,4DAAmB,CAAnB,qDAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,6DAAmB,CAAnB,sDAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,2DAAmB,CAAnB,oDAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,6DAAmB,CAAnB,sDAAmB,CAAnB,yCAAA,sBAAmB,CAAnB,2DAAmB,CAAnB,oDAAmB,CAAnB,iBAAA,eAAmB,CAAnB,eAAA,sBAAmB,CAAnB,mBAAA,kBAAmB,CAAnB,SAAA,oBAAmB,CAAnB,aAAA,kBAAmB,CAAnB,mBAAA,mBAAmB,CAAnB,kBAAA,iBAAmB,CAAnB,cAAA,oBAAmB,CAAnB,YAAA,2BAAmB,CAAnB,YAAA,uCAAmB,CAAnB,YAAA,uCAAmB,CAAnB,YAAA,oBAAmB,CAAnB,QAAA,gBAAmB,CAAnB,UAAA,cAAmB,CAAnB,UAAA,gBAAmB,CAAnB,mBAAA,kBAAmB,CAAnB,UAAA,uBAAmB,CAAnB,UAAA,qBAAmB,CAAnB,UAAA,sBAAmB,CAAnB,UAAA,oBAAmB,CAAnB,yBAAA,6BAAmB,CAAnB,iBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,eAAA,+BAAmB,CAAnB,iBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,iBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,qBAAA,gCAAmB,CAAnB,iBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,kBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,cAAA,8BAAmB,CAAnB,mBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,mBAAA,qBAAmB,CAAnB,uDAAmB,CAAnB,gBAAA,gCAAmB,CAAnB,gBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,oBAAA,wBAAmB,CAAnB,cAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,mBAAA,qBAAmB,CAAnB,wDAAmB,CAAnB,iBAAA,iBAAmB,CAAnB,qDAAmB,CAAnB,iBAAA,iBAAmB,CAAnB,qDAAmB,CAAnB,eAAA,uCAAmB,CAAnB,cAAA,8BAAmB,CAAnB,aAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,iBAAA,mCAAmB,CAAnB,YAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,aAAA,iBAAmB,CAAnB,uDAAmB,CAAnB,aAAA,iBAAmB,CAAnB,sDAAmB,CAAnB,SAAA,iCAAmB,CAAnB,iBAAA,mCAAmB,CAAnB,gBAAA,wCAAmB,CAAnB,aAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,aAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,aAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,YAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,aAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,cAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,aAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,cAAA,iBAAmB,CAAnB,sDAAmB,CAAnB,eAAA,iBAAmB,CAAnB,sDAAmB,CAAnB,UAAA,kCAAmB,CAAnB,eAAA,iBAAmB,CAAnB,uDAAmB,CAAnB,YAAA,oCAAmB,CAAnB,YAAA,oCAAmB,CAAnB,mBAAA,oCAAmB,CAAnB,eAAA,iBAAmB,CAAnB,uDAAmB,CAAnB,WAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,YAAA,iBAAmB,CAAnB,sDAAmB,CAAnB,cAAA,sCAAmB,CAAnB,gBAAA,4BAAmB,CAAnB,UAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,cAAA,oCAAmB,CAAnB,cAAA,oCAAmB,CAAnB,cAAA,qCAAmB,CAAnB,eAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,cAAA,iBAAmB,CAAnB,wDAAmB,CAAnB,eAAA,iBAAmB,CAAnB,sDAAmB,CAAnB,kLAAA,mIAAmB,CAAnB,oLAAA,qIAAmB,CAAnB,0GAAA,4FAAmB,CAAnB,0GAAA,4FAAmB,CAAnB,yGAAA,0FAAmB,CAAnB,yGAAA,0FAAmB,CAAnB,wGAAA,yFAAmB,CAAnB,0GAAA,4FAAmB,CAAnB,0GAAA,4FAAmB,CAAnB,0GAAA,4FAAmB,CAAnB,yGAAA,2FAAmB,CAAnB,0GAAA,4FAAmB,CAAnB,kBAAA,oEAAmB,CAAnB,mBAAA,0EAAmB,CAAnB,kBAAA,kEAAmB,CAAnB,kBAAA,mEAAmB,CAAnB,iBAAA,0EAAmB,CAAnB,wEAAmB,CAAnB,iEAAmB,CAAnB,eAAA,2DAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,cAAA,2DAAmB,CAAnB,kEAAmB,CAAnB,iEAAmB,CAAnB,eAAA,2DAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,mBAAA,uEAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,eAAA,2DAAmB,CAAnB,gEAAmB,CAAnB,iEAAmB,CAAnB,eAAA,2DAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,eAAA,2DAAmB,CAAnB,kEAAmB,CAAnB,iEAAmB,CAAnB,eAAA,2DAAmB,CAAnB,+DAAmB,CAAnB,iEAAmB,CAAnB,gBAAA,2DAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,gBAAA,2DAAmB,CAAnB,gEAAmB,CAAnB,iEAAmB,CAAnB,iBAAA,2DAAmB,CAAnB,kEAAmB,CAAnB,iEAAmB,CAAnB,iBAAA,2DAAmB,CAAnB,gEAAmB,CAAnB,iEAAmB,CAAnB,gBAAA,yEAAmB,CAAnB,mEAAmB,CAAnB,iEAAmB,CAAnB,iBAAA,2DAAmB,CAAnB,kEAAmB,CAAnB,iEAAmB,CAAnB,iBAAA,2DAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,iBAAA,2DAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,eAAA,2DAAmB,CAAnB,kEAAmB,CAAnB,iEAAmB,CAAnB,iBAAA,2DAAmB,CAAnB,kEAAmB,CAAnB,iEAAmB,CAAnB,iBAAA,2DAAmB,CAAnB,iEAAmB,CAAnB,iEAAmB,CAAnB,cAAA,2DAAmB,CAAnB,kEAAmB,CAAnB,iEAAmB,CAAnB,gBAAA,wEAAmB,CAAnB,wHAAmB,CAAnB,oBAAA,wEAAmB,CAAnB,6HAAmB,CAAnB,cAAA,+DAAmB,CAAnB,yGAAmB,CAAnB,eAAA,sEAAmB,CAAnB,aAAA,uDAAmB,CAAnB,YAAA,uDAAmB,CAAnB,aAAA,uDAAmB,CAAnB,gBAAA,uDAAmB,CAAnB,aAAA,uDAAmB,CAAnB,aAAA,uDAAmB,CAAnB,aAAA,uDAAmB,CAAnB,cAAA,uDAAmB,CAAnB,eAAA,uDAAmB,CAAnB,cAAA,qEAAmB,CAAnB,eAAA,uDAAmB,CAAnB,aAAA,uDAAmB,CAAnB,cAAA,uDAAmB,CAAnB,mBAAA,mEAAmB,CAAnB,eAAA,uDAAmB,CAAnB,YAAA,uDAAmB,CAAnB,YAAA,uDAAmB,CAAnB,gBAAA,2DAAmB,CAAnB,wBAAA,yBAAmB,CAAnB,cAAA,oBAAmB,CAAnB,cAAA,iBAAmB,CAAnB,WAAA,SAAmB,CAAnB,qBAAA,cAAmB,CAAnB,gBAAA,kBAAmB,CAAnB,KAAA,SAAmB,CAAnB,KAAA,cAAmB,CAAnB,KAAA,aAAmB,CAAnB,KAAA,cAAmB,CAAnB,KAAA,YAAmB,CAAnB,KAAA,cAAmB,CAAnB,KAAA,YAAmB,CAAnB,MAAA,cAAmB,CAAnB,eAAmB,CAAnB,MAAA,kBAAmB,CAAnB,mBAAmB,CAAnB,SAAA,oBAAmB,CAAnB,qBAAmB,CAAnB,MAAA,mBAAmB,CAAnB,oBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,kBAAmB,CAAnB,MAAA,mBAAmB,CAAnB,oBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,kBAAmB,CAAnB,SAAA,mBAAmB,CAAnB,sBAAmB,CAAnB,MAAA,kBAAmB,CAAnB,qBAAmB,CAAnB,SAAA,mBAAmB,CAAnB,sBAAmB,CAAnB,OAAA,kBAAmB,CAAnB,qBAAmB,CAAnB,OAAA,gBAAmB,CAAnB,mBAAmB,CAAnB,OAAA,gBAAmB,CAAnB,mBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,oBAAmB,CAAnB,SAAA,mBAAmB,CAAnB,sBAAmB,CAAnB,OAAA,gBAAmB,CAAnB,mBAAmB,CAAnB,OAAA,gBAAmB,CAAnB,mBAAmB,CAAnB,MAAA,kBAAmB,CAAnB,qBAAmB,CAAnB,MAAA,gBAAmB,CAAnB,mBAAmB,CAAnB,MAAA,mBAAmB,CAAnB,sBAAmB,CAAnB,MAAA,gBAAmB,CAAnB,mBAAmB,CAAnB,MAAA,oBAAmB,CAAnB,MAAA,qBAAmB,CAAnB,MAAA,mBAAmB,CAAnB,OAAA,mBAAmB,CAAnB,MAAA,iBAAmB,CAAnB,OAAA,kBAAmB,CAAnB,MAAA,mBAAmB,CAAnB,MAAA,aAAmB,CAAnB,MAAA,iBAAmB,CAAnB,MAAA,gBAAmB,CAAnB,MAAA,gBAAmB,CAAnB,WAAA,eAAmB,CAAnB,aAAA,iBAAmB,CAAnB,YAAA,gBAAmB,CAAnB,WAAA,mGAAmB,CAAnB,WAAA,gHAAmB,CAAnB,UAAA,gBAAmB,CAAnB,gBAAmB,CAAnB,UAAA,kBAAmB,CAAnB,mBAAmB,CAAnB,UAAA,iBAAmB,CAAnB,kBAAmB,CAAnB,UAAA,cAAmB,CAAnB,aAAmB,CAAnB,eAAA,cAAmB,CAAnB,WAAA,cAAmB,CAAnB,kBAAmB,CAAnB,SAAA,kBAAmB,CAAnB,mBAAmB,CAAnB,SAAA,iBAAmB,CAAnB,mBAAmB,CAAnB,SAAA,iBAAmB,CAAnB,mBAAmB,CAAnB,SAAA,gBAAmB,CAAnB,gBAAmB,CAAnB,WAAA,eAAmB,CAAnB,aAAA,eAAmB,CAAnB,eAAA,eAAmB,CAAnB,YAAA,yBAAmB,CAAnB,QAAA,iBAAmB,CAAnB,YAAA,iBAAmB,CAAnB,WAAA,kBAAmB,CAAnB,cAAA,aAAmB,CAAnB,iBAAA,iBAAmB,CAAnB,eAAA,gBAAmB,CAAnB,gBAAA,sBAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,mBAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,mBAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,8CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,sBAAA,iCAAmB,CAAnB,cAAA,kBAAmB,CAAnB,6BAAA,wCAAmB,CAAnB,iBAAA,4BAAmB,CAAnB,eAAA,mBAAmB,CAAnB,+CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,+CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,eAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,gBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,gBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,gBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,gBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,uBAAA,kCAAmB,CAAnB,kBAAA,mBAAmB,CAAnB,+CAAmB,CAAnB,kBAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,8CAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,yBAAA,oCAAmB,CAAnB,cAAA,yBAAmB,CAAnB,yBAAA,oCAAmB,CAAnB,cAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,cAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,cAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,cAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,2BAAA,sCAAmB,CAAnB,kBAAA,iBAAmB,CAAnB,YAAA,mBAAmB,CAAnB,+CAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,4CAAmB,CAAnB,iBAAA,mBAAmB,CAAnB,6CAAmB,CAAnB,WAAA,8BAAmB,CAAnB,cAAA,iCAAmB,CAAnB,oBAAA,yBAAmB,CAAnB,aAAA,kCAAmB,CAAnB,iCAAmB,CAAnB,WAAA,SAAmB,CAAnB,YAAA,UAAmB,CAAnB,YAAA,UAAmB,CAAnB,YAAA,UAAmB,CAAnB,QAAA,oEAAmB,CAAnB,4FAAmB,CAAnB,mBAAA,kGAAmB,CAAnB,WAAA,yEAAmB,CAAnB,iGAAmB,CAAnB,WAAA,uEAAmB,CAAnB,+FAAmB,CAAnB,wBAAA,kGAAmB,CAAnB,aAAA,qBAAmB,CAAnB,6BAAmB,CAAnB,WAAA,uCAAmB,CAAnB,sDAAmB,CAAnB,sBAAA,kGAAmB,CAAnB,WAAA,0EAAmB,CAAnB,kGAAmB,CAAnB,cAAA,6BAAmB,CAAnB,kBAAmB,CAAnB,SAAA,mBAAmB,CAAnB,QAAA,0GAAmB,CAAnB,wGAAmB,CAAnB,gBAAA,wFAAmB,CAAnB,QAAA,0GAAmB,CAAnB,wGAAmB,CAAnB,eAAA,+BAAmB,CAAnB,wBAAA,6CAAmB,CAAnB,MAAA,mBAAmB,CAAnB,gBAAA,gLAAmB,CAAnB,UAAA,oBAAmB,CAAnB,kBAAA,6BAAmB,CAAnB,oCAAA,8QAAmB,CAAnB,sQAAmB,CAAnB,kBAAA,4BAAmB,CAAnB,iBAAA,8QAAmB,CAAnB,sQAAmB,CAAnB,YAAA,6IAAmB,CAAnB,kDAAmB,CAAnB,wBAAmB,CAAnB,gBAAA,uBAAmB,CAAnB,kDAAmB,CAAnB,wBAAmB,CAAnB,mBAAA,yFAAmB,CAAnB,kDAAmB,CAAnB,wBAAmB,CAAnB,oBAAA,2BAAmB,CAAnB,kDAAmB,CAAnB,wBAAmB,CAAnB,mBAAA,8BAAmB,CAAnB,kDAAmB,CAAnB,wBAAmB,CAAnB,sBAAA,6BAAmB,CAAnB,kDAAmB,CAAnB,wBAAmB,CAAnB,cAAA,uBAAmB,CAAnB,cAAA,uBAAmB,CAAnB,aAAA,kDAAmB,CAAnB,UAAA,iDAAmB,CAAnB,iBAAA,GAAA,iCAAmB,CAAnB,sMAAmB,CAAA,CAAnB,gBAAA,GAAA,gCAAmB,CAAnB,gMAAmB,CAAA,CAAnB,YAAA,oBAAmB,CAAnB,uBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,8BAAmB,CAAnB,8BAAmB,CAAnB,WAAA,oBAAmB,CAAnB,YAAA,oBAAmB,CAAnB,cAAA,sBAAmB,CAAnB,cAAA,sBAAmB,CAAnB,aAAA,iDAAmB,CAAnB,UAAA,gDAAmB,CAAnB,qBAAA,cAAmB,CAAnB,iBAAA,UAAmB,CAAnB,2IAAA,0FAAmB,CAAnB,uBAAA,cAAmB,CAEnB,MACE,qBAAsB,CACtB,yBAA0B,CAC1B,gBAAiB,CACjB,8BAA+B,CAC/B,mBAAoB,CACpB,iCAAkC,CAClC,oBAAqB,CACrB,qCAAsC,CACtC,0BAA2B,CAC3B,mCAAoC,CACpC,sBAAuB,CACvB,iCAAkC,CAClC,uBAAwB,CACxB,gCAAiC,CACjC,2BAA4B,CAC5B,iCAAkC,CAClC,qBAAsB,CACtB,oBAAqB,CACrB,iBAAkB,CAClB,eACF,CAEA,MACE,0BAA2B,CAC3B,qBAAsB,CACtB,kBAAmB,CACnB,0BAA2B,CAC3B,iBAAkB,CAClB,6BAA8B,CAC9B,oBAAqB,CACrB,qCAAsC,CACtC,0BAA2B,CAC3B,+BAAgC,CAChC,gBAAiB,CACjB,+BAAgC,CAChC,sBAAuB,CACvB,4BAA6B,CAC7B,2BAA4B,CAC5B,sCAAuC,CACvC,uBAAwB,CACxB,sBAAuB,CACvB,iBACF,CA/CA,sCAAA,cA+JC,CA/JD,4CAAA,4BA+JC,CA/JD,qCAAA,iBA+JC,CA/JD,mBA+JC,CA/JD,yCAAA,eA+JC,CA/JD,6CAAA,4BA+JC,CA/JD,iDAAA,kCA+JC,CA/JD,6BAAA,wBA+JC,CA/JD,qDAAA,6LA+JC,CA/JD,wBAAA,iBA+JC,CA/JD,iBA+JC,CA/JD,+BAAA,kBA+JC,CA/JD,8BAAA,qBA+JC,CA/JD,wDA+JC,CA/JD,gCAAA,qBA+JC,CA/JD,wDA+JC,CA/JD,8BAAA,qBA+JC,CA/JD,wDA+JC,CA/JD,6BAAA,qBA+JC,CA/JD,wDA+JC,CA/JD,iCAAA,wBA+JC,CA/JD,8BAAA,iBA+JC,CA/JD,kDA+JC,CA/JD,wBAAA,mCA+JC,CA/JD,yBAAA,iBA+JC,CA/JD,wDA+JC,CA/JD,0BAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,0BAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,iCAAA,2CA+JC,CA/JD,iCAAA,2CA+JC,CA/JD,0BAAA,iBA+JC,CA/JD,wDA+JC,CA/JD,yBAAA,iBA+JC,CA/JD,wDA+JC,CA/JD,0BAAA,iBA+JC,CA/JD,qDA+JC,CA/JD,2BAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,2BAAA,iBA+JC,CA/JD,wDA+JC,CA/JD,4BAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,4BAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,yBAAA,iBA+JC,CA/JD,wDA+JC,CA/JD,6BAAA,uCA+JC,CA/JD,6BAAA,uCA+JC,CA/JD,wBAAA,iBA+JC,CA/JD,wDA+JC,CA/JD,yBAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,+BAAA,yCA+JC,CA/JD,2BAAA,oCA+JC,CA/JD,4BAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,4BAAA,2DA+JC,CA/JD,gEA+JC,CA/JD,iEA+JC,CA/JD,4BAAA,2DA+JC,CA/JD,+DA+JC,CA/JD,iEA+JC,CA/JD,6BAAA,2DA+JC,CA/JD,gEA+JC,CA/JD,iEA+JC,CA/JD,8BAAA,2DA+JC,CA/JD,gEA+JC,CA/JD,iEA+JC,CA/JD,6BAAA,yEA+JC,CA/JD,mEA+JC,CA/JD,iEA+JC,CA/JD,6BAAA,uDA+JC,CA/JD,0BAAA,uDA+JC,CA/JD,2BAAA,qEA+JC,CA/JD,4BAAA,uDA+JC,CA/JD,qCAAA,mCA+JC,CA/JD,4BAAA,mBA+JC,CA/JD,6CA+JC,CA/JD,4BAAA,mBA+JC,CA/JD,4CA+JC,CA/JD,8BAAA,mBA+JC,CA/JD,8CA+JC,CA/JD,2BAAA,yBA+JC,CA/JD,yBAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,wBAAA,8BA+JC,CA/JD,0BAAA,SA+JC,CA/JD,yBAAA,UA+JC,CA/JD,yBAAA,UA+JC,CA/JD,wBAAA,yEA+JC,CA/JD,iGA+JC,CA/JD,gDAAA,kGA+JC,CA/JD,wBAAA,uEA+JC,CA/JD,+FA+JC,CA/JD,wBAAA,0EA+JC,CA/JD,kGA+JC,CA/JD,kGA+JC,CA/JD,gCAAA,qBA+JC,CA/JD,uDA+JC,CA/JD,wBAAA,mCA+JC,CA/JD,qCAAA,mCA+JC,CA/JD,2BAAA,6BA+JC,CA/JD,kBA+JC,CA/JD,qBAAA,0GA+JC,CA/JD,wGA+JC,CA/JD,wFA+JC,CA/JD,gCAAA,mBA+JC,CA/JD,sDA+JC,CA/JD,gCAAA,mBA+JC,CA/JD,wDA+JC,CA/JD,8BAAA,mBA+JC,CA/JD,wDA+JC,CA/JD,wBAAA,gCA+JC,CA/JD,4BAAA,0BA+JC,CA/JD,2CAAA,6BA+JC,CA/JD,kBA+JC,CA/JD,qCAAA,0GA+JC,CA/JD,wGA+JC,CA/JD,wFA+JC,CA/JD,wCAAA,gCA+JC,CA/JD,4CAAA,0BA+JC,CA/JD,qDAAA,6CA+JC,CA/JD,gCAAA,iBA+JC,CA/JD,iBA+JC,CA/JD,6LA+JC,CA/JD,wCAAA,mBA+JC,CA/JD,mCAAA,cA+JC,CA/JD,uCAAA,kBA+JC,CA/JD,+BAAA,UA+JC,CA/JD,kCAAA,SA+JC,CA/JD,2CAAA,UA+JC,CA/JD,uCAAA,YA+JC,CA/JD,uCAAA,WA+JC,CA/JD,2CAAA,wBA+JC,CA/JD,oFAAA,6LA+JC,CA/JD,yCAAA,wBA+JC,CA/JD,yCAAA,oBA+JC,CA/JD,8EAAA,6LA+JC,CA/JD,qCAAA,gBA+JC,CA/JD,gBA+JC,CA/JD,oCAAA,gBA+JC,CA/JD,gBA+JC,CA/JD,6LA+JC,CA/JD,8CAAA,+BA+JC,CA/JD,2CAAA,mCA+JC,CA/JD,2CAAA,mCA+JC,CA/JD,6CAAA,oCA+JC,CA/JD,yCAAA,2DA+JC,CA/JD,gEA+JC,CA/JD,iEA+JC,CA/JD,yCAAA,uDA+JC,CA/JD,wCAAA,WA+JC,CA/JD,yCAAA,mBA+JC,CA/JD,6CA+JC,CA/JD,uCAAA,SA+JC,CA/JD,4DAAA,2BA+JC,CA/JD,kDAAA,kBA+JC,CA/JD,0CAAA,UA+JC,CA/JD,uDAAA,mBA+JC,CA/JD,4DAAA,wBA+JC,CA/JD,4HAAA,6LA+JC,CA/JD,gEAAA,oBA+JC,CA/JD,wBAAA,GAAA,4CA+JC,CA/JD,GAAA,QA+JC,CAAA,CA/JD,iEAAA,mCA+JC,CA/JD,0BAAA,GAAA,QA+JC,CA/JD,GAAA,4CA+JC,CAAA,CA/JD,+DAAA,qCA+JC,CA/JD,0DAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,yDAAA,oCA+JC,CA/JD,kDAAA,mCA+JC,CA/JD,qDAAA,sCA+JC,CA/JD,2DAAA,kCA+JC,CA/JD,sEAAA,oCA+JC,CA/JD,8DAAA,kCA+JC,CA/JD,8CAAA,UA+JC,CA/JD,yDAAA,uBA+JC,CA/JD,qDAAA,uBA+JC,CA/JD,mDAAA,oBA+JC,CA/JD,uBA+JC,CA/JD,0BA+JC,CA/JD,wBA+JC,CA/JD,yBA+JC,CA/JD,8BA+JC,CA/JD,8BA+JC,CA/JD,wDAAA,mBA+JC,CA/JD,uBA+JC,CA/JD,yBA+JC,CA/JD,uBA+JC,CA/JD,wBA+JC,CA/JD,6BA+JC,CA/JD,6BA+JC,CA/JD,uDAAA,mBA+JC,CA/JD,kDAAA,oBA+JC,CA/JD,wDAAA,mBA+JC,CA/JD,mDAAA,oBA+JC,CA/JD,8DAAA,8BA+JC,CA/JD,4DAAA,6BA+JC,CA/JD,6DAAA,8BA+JC,CA/JD,2DAAA,6BA+JC,CA/JD,gEAAA,0BA+JC,CA/JD,8DAAA,2BA+JC,CA/JD,mEAAA,0BA+JC,CA/JD,+DAAA,0BA+JC,CA/JD,6DAAA,2BA+JC,CA/JD,sEAAA,0BA+JC,CA/JD,6DAAA,2BA+JC,CA/JD,2DAAA,4BA+JC,CA/JD,gEAAA,2BA+JC,CA/JD,4DAAA,2BA+JC,CA/JD,0DAAA,4BA+JC,CA/JD,mEAAA,2BA+JC,CA/JD,yDAAA,sBA+JC,CA/JD,qDAAA,sBA+JC,CA/JD,uCAAA,4CAAA,oCA+JC,CAAA,CA/JD,8BAAA,kBA+JC,CA/JD,6LA+JC,CA/JD,4BAAA,gBA+JC,CA/JD,6LA+JC,CA/JD,2BAAA,cA+JC,CA/JD,cA+JC,CA/JD,6LA+JC,CA/JD,6BAAA,cA+JC,CA/JD,cA+JC,CA/JD,6LA+JC,CA/JD,mCAAA,qBA+JC,CA/JD,sDA+JC,CA/JD,mCAAA,qBA+JC,CA/JD,qDA+JC,CA/JD,mCAAA,qBA+JC,CA/JD,qDA+JC,CA/JD,mCAAA,qBA+JC,CA/JD,qDA+JC,CA/JD,uCAAA,6BA+JC,CA/JD,oCAAA,qBA+JC,CA/JD,sDA+JC,CA/JD,qCAAA,qBA+JC,CA/JD,sDA+JC,CA/JD,kCAAA,qBA+JC,CA/JD,sDA+JC,CA/JD,qCAAA,qBA+JC,CA/JD,sDA+JC,CA/JD,qCAAA,0CA+JC,CA/JD,4BAAA,iBA+JC,CA/JD,kDA+JC,CA/JD,+BAAA,iBA+JC,CA/JD,sDA+JC,CA/JD,mCAAA,kCA+JC,CA/JD,mCAAA,kCA+JC,CA/JD,+BAAA,iBA+JC,CA/JD,qDA+JC,CA/JD,mCAAA,iCA+JC,CA/JD,+BAAA,iBA+JC,CA/JD,qDA+JC,CA/JD,mCAAA,iCA+JC,CA/JD,mCAAA,kCA+JC,CA/JD,+BAAA,iBA+JC,CA/JD,qDA+JC,CA/JD,mCAAA,iCA+JC,CA/JD,oCAAA,iCA+JC,CA/JD,kCAAA,kCA+JC,CA/JD,qCAAA,kCA+JC,CA/JD,kNAAA,iJA+JC,CA/JD,gNAAA,+IA+JC,CA/JD,4HAAA,4FA+JC,CA/JD,4HAAA,4FA+JC,CA/JD,2HAAA,0FA+JC,CA/JD,2HAAA,0FA+JC,CA/JD,0HAAA,yFA+JC,CA/JD,4HAAA,4FA+JC,CA/JD,4HAAA,4FA+JC,CA/JD,4HAAA,4FA+JC,CA/JD,2HAAA,2FA+JC,CA/JD,4HAAA,4FA+JC,CA/JD,qCAAA,sEA+JC,CA/JD,gEA+JC,CA/JD,iEA+JC,CA/JD,uCAAA,sEA+JC,CA/JD,gEA+JC,CA/JD,iEA+JC,CA/JD,8BAAA,wDA+JC,CA/JD,kEA+JC,CA/JD,iEA+JC,CA/JD,gCAAA,kEA+JC,CA/JD,yGA+JC,CA/JD,mCAAA,iEA+JC,CA/JD,+BAAA,uDA+JC,CA/JD,qCAAA,kEA+JC,CA/JD,kCAAA,kEA+JC,CA/JD,4BAAA,oDA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,8CA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,iCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,kCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,kCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,kCAAA,mBA+JC,CA/JD,8CA+JC,CA/JD,oCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,mCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,gCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,gCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,gCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,8BAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,mCAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,mCAAA,mBA+JC,CA/JD,8CA+JC,CA/JD,kCAAA,oCA+JC,CA/JD,0EAAA,qCA+JC,CA/JD,uEAAA,6CA+JC,CA/JD,gDAAA,kCA+JC,CA/JD,4CAAA,iBA+JC,CA/JD,qDA+JC,CA/JD,4CAAA,iBA+JC,CA/JD,qDA+JC,CA/JD,gDAAA,iCA+JC,CA/JD,kDAAA,kCA+JC,CA/JD,gDAAA,kCA+JC,CA/JD,+CAAA,kCA+JC,CA/JD,8CAAA,mBA+JC,CA/JD,8CA+JC,CA/JD,2CAAA,mBA+JC,CA/JD,+CA+JC,CA/JD,gEAAA,iCA+JC,CA/JD,2DAAA,mBA+JC,CA/JD,8CA+JC,CA/JD,uCAAA,mEAAA,0CA+JC,CAAA,CA/JD,yBAAA,gBAAA,yBA+JC,CA/JD,UAAA,YA+JC,CA/JD,WAAA,aA+JC,CA/JD,UAAA,YA+JC,CA/JD,qBAAA,eA+JC,CA/JD,cAAA,eA+JC,CA/JD,cAAA,eA+JC,CA/JD,iBAAA,6CA+JC,CA/JD,cAAA,kBA+JC,CA/JD,iBAAA,wBA+JC,CA/JD,YAAA,QA+JC,CA/JD,WAAA,QA+JC,CA/JD,WAAA,QA+JC,CA/JD,6CAAA,sBA+JC,CA/JD,oDA+JC,CA/JD,6DA+JC,CA/JD,gBAAA,2BA+JC,CA/JD,SAAA,cA+JC,CA/JD,UAAA,mBA+JC,CA/JD,oBA+JC,CA/JD,WAAA,gBA+JC,CA/JD,mBA+JC,CA/JD,eAAA,eA+JC,CA/JD,cAAA,cA+JC,CA/JD,aA+JC,CA/JD,aAAA,iBA+JC,CA/JD,mBA+JC,CA/JD,aAAA,iBA+JC,CA/JD,mBA+JC,CA/JD,mBAAA,gBA+JC,CAAA,CA/JD,yBAAA,eAAA,SA+JC,CA/JD,aAAA,UA+JC,CA/JD,WAAA,kBA+JC,CA/JD,UAAA,oBA+JC,CA/JD,UAAA,YA+JC,CA/JD,YAAA,YA+JC,CA/JD,iBAAA,YA+JC,CA/JD,iBAAA,WA+JC,CA/JD,cAAA,eA+JC,CA/JD,iBAAA,6CA+JC,CA/JD,iBAAA,6CA+JC,CA/JD,iBAAA,6CA+JC,CA/JD,cAAA,kBA+JC,CA/JD,WAAA,UA+JC,CA/JD,UAAA,mBA+JC,CA/JD,oBA+JC,CA/JD,WAAA,gBA+JC,CA/JD,mBA+JC,CA/JD,WAAA,gBA+JC,CA/JD,mBA+JC,CA/JD,eAAA,eA+JC,CA/JD,cAAA,cA+JC,CA/JD,aA+JC,CA/JD,cAAA,iBA+JC,CA/JD,aA+JC,CA/JD,oBAAA,eA+JC,CA/JD,aAAA,kBA+JC,CA/JD,mBA+JC,CA/JD,aAAA,iBA+JC,CA/JD,mBA+JC,CAAA,CA/JD,0BAAA,gBAAA,yBA+JC,CA/JD,iBAAA,6CA+JC,CA/JD,iBAAA,6CA+JC,CA/JD,iBAAA,6CA+JC,CA/JD,UAAA,iBA+JC,CA/JD,kBA+JC,CA/JD,WAAA,gBA+JC,CA/JD,mBA+JC,CA/JD,cAAA,gBA+JC,CA/JD,gBA+JC,CA/JD,cAAA,gBA+JC,CA/JD,aA+JC,CAAA,CA/JD,yBAAA,UA+JC,CA/JD,WA+JC,CA/JD,2BAAA,aA+JC,CA/JD,kEAAA,kBA+JC,CA/JD,6LA+JC,CA/JD,qCAAA,mBA+JC,CA/JD,wBAAA,UA+JC,CA/JD,WA+JC,CA/JD,0BAAA,aA+JC", "file": "static/css/1f5b10d5c87dc685.css", "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n", null, "/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}@font-face {font-family: 'Inter Fallback';src: local(\"Arial\");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%\n}.__className_e8ce0c {font-family: 'Inter', 'Inter Fallback';font-style: normal\n}.__variable_e8ce0c {--font-inter: 'Inter', 'Inter Fallback'\n}\n\n*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n  body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n}\n.container {\n  width: 100%;\n}\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\n.badge {\n  display: inline-flex;\n  align-items: center;\n  border-radius: 9999px;\n  border-width: 1px;\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  font-weight: 600;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.badge:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-color: hsl(var(--ring));\n  --tw-ring-offset-width: 2px;\n}\n/* Improved category button styles */\n/* Better form controls */\n/* Animation utilities */\n/* 侧边栏样式增强 */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.pointer-events-auto {\n  pointer-events: auto;\n}\n.visible {\n  visibility: visible;\n}\n.fixed {\n  position: fixed;\n}\n.absolute {\n  position: absolute;\n}\n.relative {\n  position: relative;\n}\n.sticky {\n  position: sticky;\n}\n.inset-0 {\n  inset: 0px;\n}\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\n.-right-4 {\n  right: -1rem;\n}\n.bottom-0 {\n  bottom: 0px;\n}\n.bottom-4 {\n  bottom: 1rem;\n}\n.left-0 {\n  left: 0px;\n}\n.left-1\\/2 {\n  left: 50%;\n}\n.left-2 {\n  left: 0.5rem;\n}\n.left-3 {\n  left: 0.75rem;\n}\n.left-4 {\n  left: 1rem;\n}\n.left-\\[-25\\%\\] {\n  left: -25%;\n}\n.left-\\[50\\%\\] {\n  left: 50%;\n}\n.right-0 {\n  right: 0px;\n}\n.right-1 {\n  right: 0.25rem;\n}\n.right-4 {\n  right: 1rem;\n}\n.top-0 {\n  top: 0px;\n}\n.top-1 {\n  top: 0.25rem;\n}\n.top-1\\/2 {\n  top: 50%;\n}\n.top-4 {\n  top: 1rem;\n}\n.top-\\[50\\%\\] {\n  top: 50%;\n}\n.-z-10 {\n  z-index: -10;\n}\n.z-0 {\n  z-index: 0;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-30 {\n  z-index: 30;\n}\n.z-50 {\n  z-index: 50;\n}\n.z-\\[1\\] {\n  z-index: 1;\n}\n.z-\\[9\\] {\n  z-index: 9;\n}\n.col-span-3 {\n  grid-column: span 3 / span 3;\n}\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n.mb-10 {\n  margin-bottom: 2.5rem;\n}\n.mb-12 {\n  margin-bottom: 3rem;\n}\n.mb-16 {\n  margin-bottom: 4rem;\n}\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\n.mb-4 {\n  margin-bottom: 1rem;\n}\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n.mb-8 {\n  margin-bottom: 2rem;\n}\n.ml-1 {\n  margin-left: 0.25rem;\n}\n.ml-2 {\n  margin-left: 0.5rem;\n}\n.ml-4 {\n  margin-left: 1rem;\n}\n.ml-6 {\n  margin-left: 1.5rem;\n}\n.ml-auto {\n  margin-left: auto;\n}\n.mr-2 {\n  margin-right: 0.5rem;\n}\n.mr-4 {\n  margin-right: 1rem;\n}\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\n.mt-1 {\n  margin-top: 0.25rem;\n}\n.mt-12 {\n  margin-top: 3rem;\n}\n.mt-16 {\n  margin-top: 4rem;\n}\n.mt-2 {\n  margin-top: 0.5rem;\n}\n.mt-4 {\n  margin-top: 1rem;\n}\n.mt-6 {\n  margin-top: 1.5rem;\n}\n.mt-8 {\n  margin-top: 2rem;\n}\n.block {\n  display: block;\n}\n.inline-block {\n  display: inline-block;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\n.h-1 {\n  height: 0.25rem;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-11 {\n  height: 2.75rem;\n}\n.h-12 {\n  height: 3rem;\n}\n.h-16 {\n  height: 4rem;\n}\n.h-2 {\n  height: 0.5rem;\n}\n.h-24 {\n  height: 6rem;\n}\n.h-3 {\n  height: 0.75rem;\n}\n.h-3\\.5 {\n  height: 0.875rem;\n}\n.h-32 {\n  height: 8rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-6 {\n  height: 1.5rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-9 {\n  height: 2.25rem;\n}\n.h-\\[1\\.2rem\\] {\n  height: 1.2rem;\n}\n.h-\\[80px\\] {\n  height: 80px;\n}\n.h-full {\n  height: 100%;\n}\n.h-px {\n  height: 1px;\n}\n.h-screen {\n  height: 100vh;\n}\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\n.min-h-\\[calc\\(100vh-4rem\\)\\] {\n  min-height: calc(100vh - 4rem);\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-1 {\n  width: 0.25rem;\n}\n.w-1\\/3 {\n  width: 33.333333%;\n}\n.w-1\\/4 {\n  width: 25%;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-11 {\n  width: 2.75rem;\n}\n.w-12 {\n  width: 3rem;\n}\n.w-16 {\n  width: 4rem;\n}\n.w-2 {\n  width: 0.5rem;\n}\n.w-2\\/3 {\n  width: 66.666667%;\n}\n.w-20 {\n  width: 5rem;\n}\n.w-24 {\n  width: 6rem;\n}\n.w-3\\.5 {\n  width: 0.875rem;\n}\n.w-3\\/4 {\n  width: 75%;\n}\n.w-32 {\n  width: 8rem;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-48 {\n  width: 12rem;\n}\n.w-5 {\n  width: 1.25rem;\n}\n.w-6 {\n  width: 1.5rem;\n}\n.w-64 {\n  width: 16rem;\n}\n.w-8 {\n  width: 2rem;\n}\n.w-9 {\n  width: 2.25rem;\n}\n.w-\\[1\\.2rem\\] {\n  width: 1.2rem;\n}\n.w-\\[80px\\] {\n  width: 80px;\n}\n.w-fit {\n  width: fit-content;\n}\n.w-full {\n  width: 100%;\n}\n.w-screen {\n  width: 100vw;\n}\n.min-w-\\[160px\\] {\n  min-width: 160px;\n}\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-3xl {\n  max-width: 48rem;\n}\n.max-w-4xl {\n  max-width: 56rem;\n}\n.max-w-5xl {\n  max-width: 64rem;\n}\n.max-w-6xl {\n  max-width: 72rem;\n}\n.max-w-7xl {\n  max-width: 80rem;\n}\n.max-w-\\[320px\\] {\n  max-width: 320px;\n}\n.max-w-\\[600px\\] {\n  max-width: 600px;\n}\n.max-w-\\[720px\\] {\n  max-width: 720px;\n}\n.max-w-container {\n  max-width: 1280px;\n}\n.max-w-lg {\n  max-width: 32rem;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.max-w-sm {\n  max-width: 24rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.flex-none {\n  flex: none;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n.origin-left {\n  transform-origin: left;\n}\n.origin-top-right {\n  transform-origin: top right;\n}\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-3 {\n  --tw-translate-x: -0.75rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-10 {\n  --tw-translate-y: 2.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-0 {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-0 {\n  --tw-scale-x: 0;\n  --tw-scale-y: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-100 {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform-gpu {\n  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes marquee {\n\n  from {\n    transform: translateX(0);\n  }\n\n  to {\n    transform: translateX(calc(-100% - var(--gap)));\n  }\n}\n.animate-marquee {\n  animation: marquee var(--duration) linear infinite;\n}\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n.cursor-default {\n  cursor: default;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.select-none {\n  user-select: none;\n}\n.list-disc {\n  list-style-type: disc;\n}\n.auto-rows-\\[22rem\\] {\n  grid-auto-rows: 22rem;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-center {\n  align-items: center;\n}\n.items-baseline {\n  align-items: baseline;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-around {\n  justify-content: space-around;\n}\n.justify-items-center {\n  justify-items: center;\n}\n.gap-1 {\n  gap: 0.25rem;\n}\n.gap-12 {\n  gap: 3rem;\n}\n.gap-16 {\n  gap: 4rem;\n}\n.gap-2 {\n  gap: 0.5rem;\n}\n.gap-2\\.5 {\n  gap: 0.625rem;\n}\n.gap-3 {\n  gap: 0.75rem;\n}\n.gap-4 {\n  gap: 1rem;\n}\n.gap-6 {\n  gap: 1.5rem;\n}\n.gap-8 {\n  gap: 2rem;\n}\n.-space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.scroll-smooth {\n  scroll-behavior: smooth;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.rounded {\n  border-radius: 0.25rem;\n}\n.rounded-2xl {\n  border-radius: 1rem;\n}\n.rounded-\\[100px\\] {\n  border-radius: 100px;\n}\n.rounded-\\[50\\%\\] {\n  border-radius: 50%;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl {\n  border-radius: 0.75rem;\n}\n.border {\n  border-width: 1px;\n}\n.border-0 {\n  border-width: 0px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-\\[1\\.5px\\] {\n  border-width: 1.5px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-l {\n  border-left-width: 1px;\n}\n.border-r {\n  border-right-width: 1px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-\\[\\#333333\\]\\/40 {\n  border-color: rgb(51 51 51 / 0.4);\n}\n.border-blue-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\n.border-border {\n  border-color: hsl(var(--border));\n}\n.border-gray-100 {\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\n.border-gray-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-200\\/50 {\n  border-color: rgb(229 231 235 / 0.5);\n}\n.border-gray-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.border-green-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\n.border-input {\n  border-color: hsl(var(--input));\n}\n.border-orange-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\n}\n.border-orange-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));\n}\n.border-primary {\n  border-color: hsl(var(--primary));\n}\n.border-red-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n.border-transparent {\n  border-color: transparent;\n}\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-yellow-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\n}\n.bg-\\[\\#111111\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 17 17 / var(--tw-bg-opacity, 1));\n}\n.bg-\\[\\#222222\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 34 34 / var(--tw-bg-opacity, 1));\n}\n.bg-background {\n  background-color: hsl(var(--background));\n}\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\n.bg-blue-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-400\\/30 {\n  background-color: rgb(96 165 250 / 0.3);\n}\n.bg-blue-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-card {\n  background-color: hsl(var(--card));\n}\n.bg-cyan-400\\/30 {\n  background-color: rgb(34 211 238 / 0.3);\n}\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\n.bg-gray-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\n.bg-green-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\n.bg-green-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));\n}\n.bg-muted {\n  background-color: hsl(var(--muted));\n}\n.bg-orange-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\n.bg-popover {\n  background-color: hsl(var(--popover));\n}\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\n.bg-purple-400\\/30 {\n  background-color: rgb(192 132 252 / 0.3);\n}\n.bg-purple-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-red-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.bg-red-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-white\\/50 {\n  background-color: rgb(255 255 255 / 0.5);\n}\n.bg-white\\/80 {\n  background-color: rgb(255 255 255 / 0.8);\n}\n.bg-white\\/95 {\n  background-color: rgb(255 255 255 / 0.95);\n}\n.bg-yellow-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\n.bg-\\[linear-gradient\\(to_right\\2c rgba\\(0\\2c 0\\2c 0\\2c 0\\.01\\)_1px\\2c transparent_1px\\)\\2c linear-gradient\\(to_bottom\\2c rgba\\(0\\2c 0\\2c 0\\2c 0\\.01\\)_1px\\2c transparent_1px\\)\\] {\n  background-image: linear-gradient(to right,rgba(0,0,0,0.01) 1px,transparent 1px),linear-gradient(to bottom,rgba(0,0,0,0.01) 1px,transparent 1px);\n}\n.bg-\\[linear-gradient\\(to_right\\2c rgba\\(0\\2c 0\\2c 0\\2c 0\\.015\\)_1px\\2c transparent_1px\\)\\2c linear-gradient\\(to_bottom\\2c rgba\\(0\\2c 0\\2c 0\\2c 0\\.015\\)_1px\\2c transparent_1px\\)\\] {\n  background-image: linear-gradient(to right,rgba(0,0,0,0.015) 1px,transparent 1px),linear-gradient(to bottom,rgba(0,0,0,0.015) 1px,transparent 1px);\n}\n.bg-\\[radial-gradient\\(circle_600px_at_20\\%_200px\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.02\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 600px at 20% 200px,rgba(139,92,246,0.02),transparent);\n}\n.bg-\\[radial-gradient\\(circle_600px_at_50\\%_300px\\2c rgba\\(99\\2c 102\\2c 241\\2c 0\\.05\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 600px at 50% 300px,rgba(99,102,241,0.05),transparent);\n}\n.bg-\\[radial-gradient\\(circle_600px_at_50\\%_50\\%\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.03\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 600px at 50% 50%,rgba(139,92,246,0.03),transparent);\n}\n.bg-\\[radial-gradient\\(circle_600px_at_50\\%_50\\%\\2c rgba\\(59\\2c 130\\2c 246\\2c 0\\.02\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 600px at 50% 50%,rgba(59,130,246,0.02),transparent);\n}\n.bg-\\[radial-gradient\\(circle_600px_at_50\\%_50\\%\\2c rgba\\(6\\2c 182\\2c 212\\2c 0\\.03\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 600px at 50% 50%,rgba(6,182,212,0.03),transparent);\n}\n.bg-\\[radial-gradient\\(circle_600px_at_70\\%_300px\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.02\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 600px at 70% 300px,rgba(139,92,246,0.02),transparent);\n}\n.bg-\\[radial-gradient\\(circle_600px_at_80\\%_400px\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.02\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 600px at 80% 400px,rgba(139,92,246,0.02),transparent);\n}\n.bg-\\[radial-gradient\\(circle_800px_at_30\\%_200px\\2c rgba\\(59\\2c 130\\2c 246\\2c 0\\.03\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 800px at 30% 200px,rgba(59,130,246,0.03),transparent);\n}\n.bg-\\[radial-gradient\\(circle_800px_at_50\\%_200px\\2c rgba\\(6\\2c 182\\2c 212\\2c 0\\.03\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 800px at 50% 200px,rgba(6,182,212,0.03),transparent);\n}\n.bg-\\[radial-gradient\\(circle_800px_at_50\\%_400px\\2c rgba\\(59\\2c 130\\2c 246\\2c 0\\.03\\)\\2c transparent\\)\\] {\n  background-image: radial-gradient(circle 800px at 50% 400px,rgba(59,130,246,0.03),transparent);\n}\n.bg-gradient-to-b {\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-l {\n  background-image: linear-gradient(to left, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.from-background {\n  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-400 {\n  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-50 {\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-500 {\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-500\\/10 {\n  --tw-gradient-from: rgb(59 130 246 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-600 {\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-cyan-400 {\n  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-500 {\n  --tw-gradient-from: #6b7280 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(107 114 128 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-900 {\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-400 {\n  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-600 {\n  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-400 {\n  --tw-gradient-from: #818cf8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(129 140 248 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-600 {\n  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-muted\\/50 {\n  --tw-gradient-from: hsl(var(--muted) / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-100 {\n  --tw-gradient-from: #ffedd5 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 237 213 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-400 {\n  --tw-gradient-from: #fb923c var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(251 146 60 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-500 {\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-pink-400 {\n  --tw-gradient-from: #f472b6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(244 114 182 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-400 {\n  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-500 {\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-400 {\n  --tw-gradient-from: #f87171 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(248 113 113 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.via-background {\n  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background)) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-background\\/95 {\n  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.95) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-gray-800 {\n  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.to-background {\n  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);\n}\n.to-blue-600 {\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\n}\n.to-cyan-50 {\n  --tw-gradient-to: #ecfeff var(--tw-gradient-to-position);\n}\n.to-cyan-600 {\n  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);\n}\n.to-emerald-600 {\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\n}\n.to-gray-600 {\n  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);\n}\n.to-gray-700 {\n  --tw-gradient-to: #374151 var(--tw-gradient-to-position);\n}\n.to-gray-900 {\n  --tw-gradient-to: #111827 var(--tw-gradient-to-position);\n}\n.to-green-600 {\n  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);\n}\n.to-indigo-600 {\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\n}\n.to-muted\\/10 {\n  --tw-gradient-to: hsl(var(--muted) / 0.1) var(--tw-gradient-to-position);\n}\n.to-orange-600 {\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\n}\n.to-pink-600 {\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\n}\n.to-purple-50 {\n  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);\n}\n.to-purple-500\\/10 {\n  --tw-gradient-to: rgb(168 85 247 / 0.1) var(--tw-gradient-to-position);\n}\n.to-purple-600 {\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\n}\n.to-red-100 {\n  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);\n}\n.to-red-600 {\n  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);\n}\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.bg-\\[size\\:40px_40px\\] {\n  background-size: 40px 40px;\n}\n.bg-clip-text {\n  background-clip: text;\n}\n.fill-current {\n  fill: currentColor;\n}\n.fill-none {\n  fill: none;\n}\n.stroke-\\[\\#111111\\] {\n  stroke: #111111;\n}\n.object-contain {\n  object-fit: contain;\n}\n.p-0 {\n  padding: 0px;\n}\n.p-1 {\n  padding: 0.25rem;\n}\n.p-2 {\n  padding: 0.5rem;\n}\n.p-3 {\n  padding: 0.75rem;\n}\n.p-4 {\n  padding: 1rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.p-8 {\n  padding: 2rem;\n}\n.px-0 {\n  padding-left: 0px;\n  padding-right: 0px;\n}\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-10 {\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-2\\.5 {\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\n.py-20 {\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\n.py-24 {\n  padding-top: 6rem;\n  padding-bottom: 6rem;\n}\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-5 {\n  padding-top: 1.25rem;\n  padding-bottom: 1.25rem;\n}\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pb-2 {\n  padding-bottom: 0.5rem;\n}\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\n.pb-4 {\n  padding-bottom: 1rem;\n}\n.pl-10 {\n  padding-left: 2.5rem;\n}\n.pl-8 {\n  padding-left: 2rem;\n}\n.pr-12 {\n  padding-right: 3rem;\n}\n.pr-2 {\n  padding-right: 0.5rem;\n}\n.pt-0 {\n  padding-top: 0px;\n}\n.pt-2 {\n  padding-top: 0.5rem;\n}\n.pt-4 {\n  padding-top: 1rem;\n}\n.pt-8 {\n  padding-top: 2rem;\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.text-start {\n  text-align: start;\n}\n.font-mono {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n.font-sans {\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-\\[80px\\] {\n  font-size: 80px;\n}\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.capitalize {\n  text-transform: capitalize;\n}\n.italic {\n  font-style: italic;\n}\n.not-italic {\n  font-style: normal;\n}\n.leading-6 {\n  line-height: 1.5rem;\n}\n.leading-none {\n  line-height: 1;\n}\n.leading-relaxed {\n  line-height: 1.625;\n}\n.leading-tight {\n  line-height: 1.25;\n}\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\n.text-\\[\\#111111\\] {\n  --tw-text-opacity: 1;\n  color: rgb(17 17 17 / var(--tw-text-opacity, 1));\n}\n.text-\\[\\#222222\\] {\n  --tw-text-opacity: 1;\n  color: rgb(34 34 34 / var(--tw-text-opacity, 1));\n}\n.text-blue-500 {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.text-blue-700 {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.text-blue-800 {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.text-blue-900 {\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\n.text-current {\n  color: currentColor;\n}\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\n.text-foreground {\n  color: hsl(var(--foreground));\n}\n.text-gray-400 {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.text-gray-800 {\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n.text-gray-900 {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.text-green-700 {\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n.text-green-800 {\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\n.text-indigo-600 {\n  --tw-text-opacity: 1;\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\n.text-neutral-400 {\n  --tw-text-opacity: 1;\n  color: rgb(163 163 163 / var(--tw-text-opacity, 1));\n}\n.text-neutral-700 {\n  --tw-text-opacity: 1;\n  color: rgb(64 64 64 / var(--tw-text-opacity, 1));\n}\n.text-orange-500 {\n  --tw-text-opacity: 1;\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\n}\n.text-orange-800 {\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\n.text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\n.text-primary {\n  color: hsl(var(--primary));\n}\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-red-600 {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.text-red-700 {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.text-red-800 {\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\n.text-transparent {\n  color: transparent;\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-yellow-500 {\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\n.text-yellow-600 {\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\n.text-yellow-700 {\n  --tw-text-opacity: 1;\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\n}\n.text-yellow-800 {\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\n.underline {\n  text-decoration-line: underline;\n}\n.line-through {\n  text-decoration-line: line-through;\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-0 {\n  opacity: 0;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.opacity-60 {\n  opacity: 0.6;\n}\n.opacity-70 {\n  opacity: 0.7;\n}\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-none {\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.outline {\n  outline-style: solid;\n}\n.ring-0 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-1 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-black\\/5 {\n  --tw-ring-color: rgb(0 0 0 / 0.05);\n}\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-2xl {\n  --tw-blur: blur(40px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-filter {\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-200 {\n  transition-duration: 200ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\n.duration-200 {\n  animation-duration: 200ms;\n}\n.duration-300 {\n  animation-duration: 300ms;\n}\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n.\\[--duration\\:40s\\] {\n  --duration: 40s;\n}\n.\\[--gap\\:1rem\\] {\n  --gap: 1rem;\n}\n.\\[box-shadow\\:0_0_0_1px_rgba\\(0\\2c 0\\2c 0\\2c \\.03\\)\\2c 0_2px_4px_rgba\\(0\\2c 0\\2c 0\\2c \\.05\\)\\2c 0_12px_24px_rgba\\(0\\2c 0\\2c 0\\2c \\.05\\)\\] {\n  box-shadow: 0 0 0 1px rgba(0,0,0,.03),0 2px 4px rgba(0,0,0,.05),0 12px 24px rgba(0,0,0,.05);\n}\n.\\[gap\\:var\\(--gap\\)\\] {\n  gap: var(--gap);\n}\n\n:root  {\n  --background: 0 0% 97%;\n  --foreground: 240 10% 3.9%;\n  --card: 0 0% 100%;\n  --card-foreground: 240 10% 3.9%;\n  --popover: 0 0% 100%;\n  --popover-foreground: 240 10% 3.9%;\n  --primary: 15 85% 50%;\n  --primary-foreground: 355.7 100% 97.3%;\n  --secondary: 240 4.8% 95.9%;\n  --secondary-foreground: 240 5.9% 10%;\n  --muted: 240 4.8% 95.9%;\n  --muted-foreground: 240 3.8% 46.1%;\n  --accent: 240 4.8% 95.9%;\n  --accent-foreground: 240 5.9% 10%;\n  --destructive: 0 84.2% 60.2%;\n  --destructive-foreground: 0 0% 98%;\n  --border: 240 5.9% 90%;\n  --input: 240 5.9% 90%;\n  --ring: 15 85% 50%;\n  --radius: 0.5rem\n}\n\n.dark  {\n  --background: 20 14.3% 4.1%;\n  --foreground: 0 0% 95%;\n  --card: 24 9.8% 10%;\n  --card-foreground: 0 0% 95%;\n  --popover: 0 0% 9%;\n  --popover-foreground: 0 0% 95%;\n  --primary: 15 85% 50%;\n  --primary-foreground: 355.7 100% 97.3%;\n  --secondary: 240 3.7% 15.9%;\n  --secondary-foreground: 0 0% 98%;\n  --muted: 0 0% 15%;\n  --muted-foreground: 240 5% 64.9%;\n  --accent: 12 6.5% 15.1%;\n  --accent-foreground: 0 0% 98%;\n  --destructive: 0 62.8% 30.6%;\n  --destructive-foreground: 0 85.7% 97.3%;\n  --border: 240 3.7% 15.9%;\n  --input: 240 3.7% 15.9%;\n  --ring: 15 85% 50%\n}\n\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\n\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\n\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\n\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\n\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\n\n.hover\\:-translate-y-2:hover {\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:rounded-\\[12px\\]:hover {\n  border-radius: 12px;\n}\n\n.hover\\:border-blue-200:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-indigo-200:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-pink-200:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(251 207 232 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-red-200:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-transparent:hover {\n  border-color: transparent;\n}\n\n.hover\\:bg-\\[\\#000000\\]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\n\n.hover\\:bg-blue-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-blue-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-blue-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\n\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\n\n.hover\\:bg-gray-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-gray-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-gray-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-green-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-indigo-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-indigo-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-orange-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-pink-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\n\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\n\n.hover\\:bg-red-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-red-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\n\n.hover\\:bg-white\\/80:hover {\n  background-color: rgb(255 255 255 / 0.8);\n}\n\n.hover\\:bg-yellow-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:from-blue-700:hover {\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-gray-800:hover {\n  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-green-700:hover {\n  --tw-gradient-from: #15803d var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(21 128 61 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-indigo-700:hover {\n  --tw-gradient-from: #4338ca var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(67 56 202 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:from-muted\\/60:hover {\n  --tw-gradient-from: hsl(var(--muted) / 0.6) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:to-emerald-700:hover {\n  --tw-gradient-to: #047857 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-gray-600:hover {\n  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);\n}\n\n.hover\\:to-muted\\/20:hover {\n  --tw-gradient-to: hsl(var(--muted) / 0.2) var(--tw-gradient-to-position);\n}\n\n.hover\\:to-purple-700:hover {\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\n}\n\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n\n.hover\\:text-blue-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-gray-900:hover {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-indigo-500:hover {\n  --tw-text-opacity: 1;\n  color: rgb(99 102 241 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-primary:hover {\n  color: hsl(var(--primary));\n}\n\n.hover\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\n\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\n\n.hover\\:opacity-70:hover {\n  opacity: 0.7;\n}\n\n.hover\\:opacity-90:hover {\n  opacity: 0.9;\n}\n\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-md:hover {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.focus\\:border-indigo-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));\n}\n\n.focus\\:bg-accent:focus {\n  background-color: hsl(var(--accent));\n}\n\n.focus\\:text-accent-foreground:focus {\n  color: hsl(var(--accent-foreground));\n}\n\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-\\[\\#24292F\\]:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(36 41 47 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-\\[\\#4285F4\\]:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(66 133 244 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-indigo-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:ring-offset-background:focus-visible {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n\n.active\\:scale-\\[0\\.95\\]:active {\n  --tw-scale-x: 0.95;\n  --tw-scale-y: 0.95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\n\n.disabled\\:transform-none:disabled {\n  transform: none;\n}\n\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\n\n.group:hover .group-hover\\:left-4 {\n  left: 1rem;\n}\n\n.group:hover .group-hover\\:right-\\[-25\\%\\] {\n  right: -25%;\n}\n\n.group:hover .group-hover\\:h-\\[220px\\] {\n  height: 220px;\n}\n\n.group:hover .group-hover\\:w-\\[220px\\] {\n  width: 220px;\n}\n\n.group:hover .group-hover\\:-translate-y-10 {\n  --tw-translate-y: -2.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:translate-x-3 {\n  --tw-translate-x: 0.75rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:translate-y-0 {\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-110 {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-75 {\n  --tw-scale-x: .75;\n  --tw-scale-y: .75;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:bg-black\\/\\[\\.03\\] {\n  background-color: rgb(0 0 0 / .03);\n}\n\n.group:hover .group-hover\\:bg-blue-400\\/60 {\n  background-color: rgb(96 165 250 / 0.6);\n}\n\n.group:hover .group-hover\\:bg-cyan-400\\/60 {\n  background-color: rgb(34 211 238 / 0.6);\n}\n\n.group:hover .group-hover\\:bg-purple-400\\/60 {\n  background-color: rgb(192 132 252 / 0.6);\n}\n\n.group:hover .group-hover\\:from-blue-700 {\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.group:hover .group-hover\\:to-purple-700 {\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\n}\n\n.group:hover .group-hover\\:stroke-white {\n  stroke: #fff;\n}\n\n.group:hover .group-hover\\:text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:\\[animation-play-state\\:paused\\] {\n  animation-play-state: paused;\n}\n\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\n\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\n\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\n\n.data-\\[state\\=checked\\]\\:translate-x-5[data-state=\"checked\"] {\n  --tw-translate-x: 1.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n@keyframes accordion-up {\n\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n\n  to {\n    height: 0;\n  }\n}\n\n.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=\"closed\"] {\n  animation: accordion-up 0.2s ease-out;\n}\n\n@keyframes accordion-down {\n\n  from {\n    height: 0;\n  }\n\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\n\n.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=\"open\"] {\n  animation: accordion-down 0.2s ease-out;\n}\n\n.data-\\[state\\=checked\\]\\:bg-blue-600[data-state=\"checked\"] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\n\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\n\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"] {\n  background-color: hsl(var(--secondary));\n}\n\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"] {\n  background-color: hsl(var(--input));\n}\n\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\n\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"] {\n  color: hsl(var(--muted-foreground));\n}\n\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\n\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\n\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\n\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\n\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\n\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\n\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"] {\n  --tw-exit-translate-y: -48%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"] {\n  --tw-enter-translate-y: -48%;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\n\n@supports (backdrop-filter: var(--tw)) {\n\n  .supports-\\[backdrop-filter\\]\\:bg-white\\/60 {\n    background-color: rgb(255 255 255 / 0.6);\n  }\n}\n\n.dark\\:-rotate-90:is(.dark *) {\n  --tw-rotate: -90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.dark\\:rotate-0:is(.dark *) {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.dark\\:scale-0:is(.dark *) {\n  --tw-scale-x: 0;\n  --tw-scale-y: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.dark\\:scale-100:is(.dark *) {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.dark\\:border-blue-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-gray-600:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-gray-700:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-gray-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-gray-800\\/50:is(.dark *) {\n  border-color: rgb(31 41 55 / 0.5);\n}\n\n.dark\\:border-green-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-orange-700:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-red-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-yellow-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:bg-background\\/80:is(.dark *) {\n  background-color: hsl(var(--background) / 0.8);\n}\n\n.dark\\:bg-black:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:bg-blue-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:bg-blue-800\\/30:is(.dark *) {\n  background-color: rgb(30 64 175 / 0.3);\n}\n\n.dark\\:bg-blue-900\\/20:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.2);\n}\n\n.dark\\:bg-gray-700:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:bg-gray-700\\/50:is(.dark *) {\n  background-color: rgb(55 65 81 / 0.5);\n}\n\n.dark\\:bg-gray-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:bg-gray-800\\/50:is(.dark *) {\n  background-color: rgb(31 41 55 / 0.5);\n}\n\n.dark\\:bg-gray-800\\/95:is(.dark *) {\n  background-color: rgb(31 41 55 / 0.95);\n}\n\n.dark\\:bg-gray-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:bg-gray-900\\/50:is(.dark *) {\n  background-color: rgb(17 24 39 / 0.5);\n}\n\n.dark\\:bg-green-900\\/20:is(.dark *) {\n  background-color: rgb(20 83 45 / 0.2);\n}\n\n.dark\\:bg-red-900\\/20:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.2);\n}\n\n.dark\\:bg-yellow-900\\/20:is(.dark *) {\n  background-color: rgb(113 63 18 / 0.2);\n}\n\n.dark\\:bg-\\[linear-gradient\\(to_right\\2c rgba\\(255\\2c 255\\2c 255\\2c 0\\.015\\)_1px\\2c transparent_1px\\)\\2c linear-gradient\\(to_bottom\\2c rgba\\(255\\2c 255\\2c 255\\2c 0\\.015\\)_1px\\2c transparent_1px\\)\\]:is(.dark *) {\n  background-image: linear-gradient(to right,rgba(255,255,255,0.015) 1px,transparent 1px),linear-gradient(to bottom,rgba(255,255,255,0.015) 1px,transparent 1px);\n}\n\n.dark\\:bg-\\[linear-gradient\\(to_right\\2c rgba\\(255\\2c 255\\2c 255\\2c 0\\.02\\)_1px\\2c transparent_1px\\)\\2c linear-gradient\\(to_bottom\\2c rgba\\(255\\2c 255\\2c 255\\2c 0\\.02\\)_1px\\2c transparent_1px\\)\\]:is(.dark *) {\n  background-image: linear-gradient(to right,rgba(255,255,255,0.02) 1px,transparent 1px),linear-gradient(to bottom,rgba(255,255,255,0.02) 1px,transparent 1px);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_600px_at_20\\%_200px\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.04\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 600px at 20% 200px,rgba(139,92,246,0.04),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_600px_at_50\\%_300px\\2c rgba\\(99\\2c 102\\2c 241\\2c 0\\.08\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 600px at 50% 300px,rgba(99,102,241,0.08),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_600px_at_50\\%_50\\%\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.06\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 600px at 50% 50%,rgba(139,92,246,0.06),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_600px_at_50\\%_50\\%\\2c rgba\\(59\\2c 130\\2c 246\\2c 0\\.04\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 600px at 50% 50%,rgba(59,130,246,0.04),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_600px_at_50\\%_50\\%\\2c rgba\\(6\\2c 182\\2c 212\\2c 0\\.06\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 600px at 50% 50%,rgba(6,182,212,0.06),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_600px_at_70\\%_300px\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.04\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 600px at 70% 300px,rgba(139,92,246,0.04),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_600px_at_80\\%_400px\\2c rgba\\(139\\2c 92\\2c 246\\2c 0\\.04\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 600px at 80% 400px,rgba(139,92,246,0.04),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_800px_at_30\\%_200px\\2c rgba\\(59\\2c 130\\2c 246\\2c 0\\.06\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 800px at 30% 200px,rgba(59,130,246,0.06),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_800px_at_50\\%_200px\\2c rgba\\(6\\2c 182\\2c 212\\2c 0\\.06\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 800px at 50% 200px,rgba(6,182,212,0.06),transparent);\n}\n\n.dark\\:bg-\\[radial-gradient\\(circle_800px_at_50\\%_400px\\2c rgba\\(59\\2c 130\\2c 246\\2c 0\\.06\\)\\2c transparent\\)\\]:is(.dark *) {\n  background-image: radial-gradient(circle 800px at 50% 400px,rgba(59,130,246,0.06),transparent);\n}\n\n.dark\\:from-blue-900\\/20:is(.dark *) {\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.dark\\:from-orange-900\\/30:is(.dark *) {\n  --tw-gradient-from: rgb(124 45 18 / 0.3) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.dark\\:from-white:is(.dark *) {\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.dark\\:via-gray-100:is(.dark *) {\n  --tw-gradient-to: rgb(243 244 246 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #f3f4f6 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n\n.dark\\:to-cyan-900\\/20:is(.dark *) {\n  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);\n}\n\n.dark\\:to-gray-300:is(.dark *) {\n  --tw-gradient-to: #d1d5db var(--tw-gradient-to-position);\n}\n\n.dark\\:to-purple-900\\/20:is(.dark *) {\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\n}\n\n.dark\\:to-red-900\\/30:is(.dark *) {\n  --tw-gradient-to: rgb(127 29 29 / 0.3) var(--tw-gradient-to-position);\n}\n\n.dark\\:to-white:is(.dark *) {\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\n}\n\n.dark\\:text-blue-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-blue-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-blue-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-blue-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-gray-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-gray-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-gray-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-gray-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-green-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(187 247 208 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-green-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-green-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-neutral-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(212 212 212 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-orange-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 215 170 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-red-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-red-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-red-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-white:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-yellow-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-yellow-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:ring-white\\/10:is(.dark *) {\n  --tw-ring-color: rgb(255 255 255 / 0.1);\n}\n\n.dark\\:\\[border\\:1px_solid_rgba\\(255\\2c 255\\2c 255\\2c \\.1\\)\\]:is(.dark *) {\n  border: 1px solid rgba(255,255,255,.1);\n}\n\n.dark\\:\\[box-shadow\\:0_-20px_80px_-20px_\\#ffffff1f_inset\\]:is(.dark *) {\n  box-shadow: 0 -20px 80px -20px #ffffff1f inset;\n}\n\n.dark\\:hover\\:bg-blue-900\\/20:hover:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.2);\n}\n\n.dark\\:hover\\:bg-gray-700:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:hover\\:bg-gray-800:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:hover\\:bg-gray-900\\/80:hover:is(.dark *) {\n  background-color: rgb(17 24 39 / 0.8);\n}\n\n.dark\\:hover\\:bg-indigo-900\\/20:hover:is(.dark *) {\n  background-color: rgb(49 46 129 / 0.2);\n}\n\n.dark\\:hover\\:bg-pink-900\\/20:hover:is(.dark *) {\n  background-color: rgb(131 24 67 / 0.2);\n}\n\n.dark\\:hover\\:bg-red-900\\/20:hover:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.2);\n}\n\n.dark\\:hover\\:text-blue-400:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:hover\\:text-white:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.group:hover .group-hover\\:dark\\:bg-neutral-800\\/10:is(.dark *) {\n  background-color: rgb(38 38 38 / 0.1);\n}\n\n.group:hover .dark\\:group-hover\\:text-blue-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n\n@supports (backdrop-filter: var(--tw)) {\n\n  .dark\\:supports-\\[backdrop-filter\\]\\:bg-background\\/60:is(.dark *) {\n    background-color: hsl(var(--background) / 0.6);\n  }\n}\n\n@media (min-width: 640px) {\n\n  .sm\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:grid {\n    display: grid;\n  }\n\n  .sm\\:max-w-\\[320px\\] {\n    max-width: 320px;\n  }\n\n  .sm\\:max-w-md {\n    max-width: 28rem;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:gap-16 {\n    gap: 4rem;\n  }\n\n  .sm\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .sm\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:rounded-lg {\n    border-radius: var(--radius);\n  }\n\n  .sm\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:py-24 {\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .sm\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .sm\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .sm\\:leading-tight {\n    line-height: 1.25;\n  }\n}\n\n@media (min-width: 768px) {\n\n  .md\\:left-auto {\n    left: auto;\n  }\n\n  .md\\:right-4 {\n    right: 1rem;\n  }\n\n  .md\\:mb-12 {\n    margin-bottom: 3rem;\n  }\n\n  .md\\:mb-6 {\n    margin-bottom: 1.5rem;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:h-\\[120px\\] {\n    height: 120px;\n  }\n\n  .md\\:w-\\[120px\\] {\n    width: 120px;\n  }\n\n  .md\\:max-w-md {\n    max-width: 28rem;\n  }\n\n  .md\\:grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .md\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .md\\:py-24 {\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n\n  .md\\:py-32 {\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n  }\n\n  .md\\:text-left {\n    text-align: left;\n  }\n\n  .md\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-\\[120px\\] {\n    font-size: 120px;\n  }\n\n  .md\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\n\n@media (min-width: 1024px) {\n\n  .lg\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n\n  .lg\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .lg\\:py-32 {\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n  }\n\n  .lg\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .lg\\:text-7xl {\n    font-size: 4.5rem;\n    line-height: 1;\n  }\n}\n\n.\\[\\&\\>svg\\]\\:size-4>svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:shrink-0>svg {\n  flex-shrink: 0;\n}\n\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\n\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\n", "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n:root  {\n  --background: 0 0% 97%;\n  --foreground: 240 10% 3.9%;\n  --card: 0 0% 100%;\n  --card-foreground: 240 10% 3.9%;\n  --popover: 0 0% 100%;\n  --popover-foreground: 240 10% 3.9%;\n  --primary: 15 85% 50%;\n  --primary-foreground: 355.7 100% 97.3%;\n  --secondary: 240 4.8% 95.9%;\n  --secondary-foreground: 240 5.9% 10%;\n  --muted: 240 4.8% 95.9%;\n  --muted-foreground: 240 3.8% 46.1%;\n  --accent: 240 4.8% 95.9%;\n  --accent-foreground: 240 5.9% 10%;\n  --destructive: 0 84.2% 60.2%;\n  --destructive-foreground: 0 0% 98%;\n  --border: 240 5.9% 90%;\n  --input: 240 5.9% 90%;\n  --ring: 15 85% 50%;\n  --radius: 0.5rem\n}\n\n.dark  {\n  --background: 20 14.3% 4.1%;\n  --foreground: 0 0% 95%;\n  --card: 24 9.8% 10%;\n  --card-foreground: 0 0% 95%;\n  --popover: 0 0% 9%;\n  --popover-foreground: 0 0% 95%;\n  --primary: 15 85% 50%;\n  --primary-foreground: 355.7 100% 97.3%;\n  --secondary: 240 3.7% 15.9%;\n  --secondary-foreground: 0 0% 98%;\n  --muted: 0 0% 15%;\n  --muted-foreground: 240 5% 64.9%;\n  --accent: 12 6.5% 15.1%;\n  --accent-foreground: 0 0% 98%;\n  --destructive: 0 62.8% 30.6%;\n  --destructive-foreground: 0 85.7% 97.3%;\n  --border: 240 3.7% 15.9%;\n  --input: 240 3.7% 15.9%;\n  --ring: 15 85% 50%\n}\n\n@layer base {\n  body {\n    @apply bg-background text-foreground;\n  }\n}\n\n@layer components {\n  .tool-card {\n    @apply relative bg-card border border-border p-5 rounded-xl transition-all duration-300;\n  }\n  \n  .tool-card:hover {\n    @apply shadow-md border-primary/20 -translate-y-1;\n  }\n\n  .tool-card-featured {\n    @apply relative bg-gradient-to-br from-accent to-background border border-primary/20 p-5 rounded-xl transition-all duration-300;\n  }\n  \n  .tool-card-featured:hover {\n    @apply shadow-lg border-primary/40 -translate-y-1;\n  }\n\n  .badge {\n    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;\n  }\n\n  .badge-new {\n    @apply badge bg-green-500 text-white border-transparent;\n  }\n\n  .badge-featured {\n    @apply badge bg-amber-500 text-white border-transparent;\n  }\n\n  .badge-free {\n    @apply badge bg-green-500 text-white border-transparent;\n  }\n\n  .badge-premium {\n    @apply badge bg-amber-500 text-white border-transparent;\n  }\n\n  .nav-link {\n    @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;\n  }\n\n  .nav-link-active {\n    @apply bg-accent text-accent-foreground;\n  }\n\n  .search-input {\n    @apply w-full bg-background/70 backdrop-blur-sm rounded-full px-5 py-3 border border-border focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-base;\n  }\n\n  .glass-card {\n    @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-sm dark:bg-gray-900/70;\n  }\n\n  .glass-effect {\n    @apply bg-white/80 backdrop-blur-md dark:bg-gray-900/80;\n  }\n  \n  /* Improved category button styles */\n  .category-button {\n    @apply flex items-center gap-2 px-4 py-3 rounded-xl border border-sidebar-border bg-sidebar-background hover:bg-sidebar-hover hover:border-sidebar-primary/30 hover:shadow-sm transition-all;\n  }\n  \n  .category-button-active {\n    @apply border-sidebar-primary/50 bg-sidebar-accent shadow-sm;\n  }\n  \n  /* Better form controls */\n  .form-input {\n    @apply rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all;\n  }\n  \n  /* Animation utilities */\n  .hover-scale {\n    @apply transition-transform duration-200 hover:scale-105;\n  }\n  \n  .hover-lift {\n    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-md;\n  }\n  \n  /* 侧边栏样式增强 */\n  .sidebar-header {\n    @apply border-b border-sidebar-border pb-3 mb-4;\n  }\n  \n  .sidebar-title {\n    @apply text-lg font-semibold text-sidebar-foreground;\n  }\n  \n  .sidebar-category-icon {\n    @apply text-sidebar-primary transition-colors;\n  }\n  \n  .sidebar-category-name {\n    @apply text-sidebar-foreground transition-colors;\n  }\n  \n  .sidebar-category-item {\n    @apply rounded-lg transition-all hover:bg-sidebar-hover p-2 flex items-center gap-3 cursor-pointer;\n  }\n  \n  .sidebar-category-item-active {\n    @apply bg-sidebar-accent text-sidebar-accent-foreground;\n  }\n}"], "sourceRoot": "", "ignoreList": [0]}