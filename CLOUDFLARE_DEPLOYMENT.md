# 🚀 Cloudflare Pages 部署指南

本项目现在使用 **OpenNext.js Cloudflare** 适配器，完美解决了 NextAuth 与 Edge Runtime 的兼容性问题。

## 🎯 解决方案概述

- ✅ **完美兼容**: 使用 OpenNext.js Cloudflare 适配器，完全解决 NextAuth 与 Edge Runtime 兼容性问题
- ✅ **智能运行时**: 自动为不同类型的路由选择最适合的运行时环境
- ✅ **零配置部署**: 一键构建和部署到 Cloudflare Pages
- ✅ **性能优化**: 利用 Cloudflare 全球 CDN 和边缘计算网络
- ✅ **成本效益**: Cloudflare Pages 提供慷慨的免费额度

## 🚀 构建成功！

项目已成功配置 OpenNext.js Cloudflare 适配器：

```bash
✅ Next.js 构建完成
✅ OpenNext.js Cloudflare 适配器处理完成
✅ Worker 文件生成: .open-next/worker.js
✅ 本地预览服务器运行在: http://localhost:8788
```

## 📦 本地开发

### 1. 安装依赖
```bash
pnpm install
```

### 2. 启动开发服务器
```bash
# 标准 Next.js 开发
pnpm dev

# 或者使用 Cloudflare 兼容模式预览
pnpm build:cloudflare
pnpm preview:cloudflare
```

## 🏗️ 构建和部署

### 方法 1: 自动部署（推荐）

1. **连接 GitHub 到 Cloudflare Pages**
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 进入 Pages > Create a project > Connect to Git
   - 选择您的 GitHub 仓库

2. **配置构建设置**
   ```
   构建命令: pnpm run build:cloudflare
   输出目录: .open-next/worker.js
   Node.js 版本: 18 或更高
   ```

3. **设置环境变量**
   在 Cloudflare Pages 项目设置中添加：
   ```
   DATABASE_URL=your_database_url
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=https://your-domain.pages.dev
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   STRIPE_PRIVATE_KEY=your_stripe_private_key
   STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
   GITHUB_TOKEN=your_github_token
   ```

### 方法 2: 手动部署

```bash
# 1. 构建项目
pnpm run build:cloudflare

# 2. 部署到 Cloudflare Pages
pnpm run deploy:cloudflare
```

## ⚙️ 配置说明

### OpenNext.js 配置 (`open-next.config.ts`)

项目使用智能路由配置：

- **Node.js 运行时路由**:
  - `/api/auth/*` - NextAuth 认证
  - `/api/auth/signup` - 用户注册（bcryptjs）
  - `/api/orders/*` - 订单管理
  - `/api/stripe/*` - 支付处理
  - `/[locale]/profile` - 用户资料页面
  - `/[locale]/auth/signin` - 登录页面

- **Edge 运行时路由**:
  - `/api/github/*` - GitHub API 调用
  - `/[locale]/demo/*` - 演示页面
  - 静态页面和资源

### Wrangler 配置 (`wrangler.toml`)

- 启用 `nodejs_compat` 兼容性标志
- 配置环境变量和绑定
- 支持开发和生产环境

## 🔧 故障排除

### 常见问题

1. **构建失败 - "Module not found"**
   ```bash
   # 清理缓存并重新安装
   rm -rf .next .open-next node_modules
   pnpm install
   pnpm run build:cloudflare
   ```

2. **NextAuth 认证失败**
   - 确保 `NEXTAUTH_URL` 设置为正确的域名
   - 检查 `NEXTAUTH_SECRET` 是否配置
   - 验证 OAuth 提供商的回调 URL

3. **数据库连接问题**
   - 确保 `DATABASE_URL` 格式正确
   - 检查数据库是否可从 Cloudflare 网络访问
   - 考虑使用 Cloudflare D1 或兼容的数据库

4. **环境变量未生效**
   - 在 Cloudflare Pages 控制台中重新部署
   - 检查变量名是否正确（区分大小写）
   - 确保敏感变量没有在代码中硬编码

### 调试技巧

1. **查看构建日志**
   ```bash
   pnpm run build:cloudflare --verbose
   ```

2. **本地测试 Cloudflare 环境**
   ```bash
   pnpm run preview:cloudflare
   ```

3. **检查 Worker 输出**
   ```bash
   ls -la .open-next/
   cat .open-next/worker.js | head -50
   ```

## 📊 性能优化

### 自动优化功能

- ✅ **代码分割**: 自动分离 Node.js 和 Edge 运行时代码
- ✅ **静态资源**: CDN 缓存和压缩
- ✅ **边缘计算**: 全球分布的计算节点
- ✅ **智能路由**: 根据路由类型选择最佳运行时

### 监控和分析

- 使用 Cloudflare Analytics 监控性能
- 设置 Real User Monitoring (RUM)
- 配置 Web Vitals 跟踪

## 🔗 相关链接

- [OpenNext.js Cloudflare 文档](https://opennext.js.org/cloudflare)
- [Cloudflare Pages 文档](https://developers.cloudflare.com/pages/)
- [Wrangler CLI 文档](https://developers.cloudflare.com/workers/wrangler/)
- [NextAuth.js 文档](https://next-auth.js.org/)

## 🆘 获取帮助

如果遇到问题：

1. 查看 [OpenNext.js GitHub Issues](https://github.com/opennextjs/opennextjs-cloudflare/issues)
2. 检查 [Cloudflare Community](https://community.cloudflare.com/)
3. 参考项目的 GitHub Issues

---

💡 **提示**: 这个配置已经过优化，可以充分利用 Cloudflare Pages 的优势，同时保持与现有 NextAuth 和数据库集成的完全兼容性。
