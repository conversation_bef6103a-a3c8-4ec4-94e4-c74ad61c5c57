/**
 * Order Activation API Route / 订单激活 API 路由
 *
 * @description Handles order activation for paid orders. Updates order status
 * and processes any necessary activation logic.
 * @description 处理已支付订单的激活。更新订单状态并处理必要的激活逻辑。
 *
 * @routes
 * - POST /api/orders/activate - Activate a paid order
 *
 * @路由
 * - POST /api/orders/activate - 激活已支付的订单
 *
 * @access Private - Requires user authentication
 * @access 私有 - 需要用户认证
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */

import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { config as authOptions } from '@/auth.config';

export const runtime = 'edge';

/**
 * Activation request interface
 * 激活请求接口
 */
interface ActivationRequest {
  /** Order number to activate / 要激活的订单号 */
  orderNo: string;
}

/**
 * Activation response interface
 * 激活响应接口
 */
interface ActivationResponse {
  /** Success status / 成功状态 */
  success: boolean;
  /** Response message / 响应消息 */
  message: string;
  /** Updated order data / 更新的订单数据 */
  order?: any;
}

/**
 * Error response interface
 * 错误响应接口
 */
interface ErrorResponse {
  /** Error message / 错误消息 */
  error: string;
}

/**
 * POST /api/orders/activate - Activate a paid order
 * POST /api/orders/activate - 激活已支付的订单
 *
 * @description Activates a paid order by updating its status and processing
 * any necessary activation logic such as granting credits or access.
 * @description 通过更新状态和处理必要的激活逻辑（如授予积分或访问权限）来激活已支付的订单。
 *
 * @authentication Required - User must be logged in and own the order
 * @authentication 必需 - 用户必须已登录且拥有该订单
 *
 * @requestBody ActivationRequest - Order activation data
 * @requestBody ActivationRequest - 订单激活数据
 *
 * @responses
 * - 200: ActivationResponse - Order activated successfully
 * - 200: ActivationResponse - 订单激活成功
 * - 400: ErrorResponse - Invalid request or order not eligible for activation
 * - 400: ErrorResponse - 无效请求或订单不符合激活条件
 * - 401: ErrorResponse - User not authenticated
 * - 401: ErrorResponse - 用户未认证
 * - 403: ErrorResponse - User not authorized to activate this order
 * - 403: ErrorResponse - 用户无权激活此订单
 * - 404: ErrorResponse - Order not found
 * - 404: ErrorResponse - 订单未找到
 * - 500: ErrorResponse - Internal server error
 * - 500: ErrorResponse - 内部服务器错误
 */
export async function POST(request: Request) {
  try {
    // Authenticate user session / 认证用户会话
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body / 解析请求体
    const { orderNo }: ActivationRequest = await request.json();

    if (!orderNo) {
      return NextResponse.json({ error: 'Order number is required' }, { status: 400 });
    }

    // Find user in database / 在数据库中查找用户
    const user = await prisma.user.findFirst({
      where: {
        email: session.user.email,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Find the order and verify ownership / 查找订单并验证所有权
    const order = await prisma.order.findFirst({
      where: {
        orderNo: orderNo,
        userUuid: user.uuid,
      },
    });

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    // Check if order is already activated / 检查订单是否已激活
    if (order.status === 'activated') {
      return NextResponse.json({
        error: 'Order is already activated'
      }, { status: 400 });
    }

    // Check if order is paid / 检查订单是否已支付
    if (order.status !== 'paid') {
      return NextResponse.json({
        error: 'Order must be paid before activation'
      }, { status: 400 });
    }

    // Update order status to activated / 更新订单状态为已激活
    const updatedOrder = await prisma.order.update({
      where: {
        orderNo: orderNo,
      },
      data: {
        status: 'activated',
        updatedAt: new Date(),
        // Add any additional activation logic here
        // 在此处添加任何额外的激活逻辑
      },
    });

    // TODO: Add any additional activation logic here such as:
    // - Granting user credits
    // - Enabling premium features
    // - Sending activation email
    // - Creating user subscriptions
    // 
    // 待办：在此处添加任何额外的激活逻辑，例如：
    // - 授予用户积分
    // - 启用高级功能
    // - 发送激活邮件
    // - 创建用户订阅

    console.log(`Order ${orderNo} activated successfully for user ${user.email}`);

    return NextResponse.json({
      success: true,
      message: 'Order activated successfully',
      order: updatedOrder
    });

  } catch (error) {
    console.error('Error activating order:', error);
    return NextResponse.json({ 
      error: 'Failed to activate order' 
    }, { status: 500 });
  }
}
