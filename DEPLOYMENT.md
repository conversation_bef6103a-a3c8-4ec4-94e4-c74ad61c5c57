# 🚀 部署指南

本项目支持多种部署平台，并提供了智能配置系统来自动适配不同的部署环境。

## 📋 支持的平台

- ☁️ **Cloudflare Pages** - Edge Runtime，全球 CDN（⚠️ 部分限制）
- ▲ **Vercel** - 无服务器函数，自动扩展（✅ 推荐）
- 🌐 **Netlify** - JAMstack 部署
- 🐳 **Docker** - 容器化部署，自托管

## ⚠️ Cloudflare Pages 重要说明

由于 NextAuth v4 依赖 Node.js 特定的模块（如 `crypto`），它与 Cloudflare Pages 的 Edge Runtime 不完全兼容。

### 解决方案选择：

1. **推荐：使用 Vercel 部署** - 完全兼容，零配置
2. **升级到 NextAuth v5** - 原生支持 Edge Runtime（需要代码重构）
3. **混合部署** - 静态资源用 Cloudflare，API 用其他平台

## 🎯 快速部署

### 方法 1: 自动检测部署（推荐）

项目会自动检测部署环境并应用相应配置：

```bash
# 构建项目（自动检测环境）
npm run build

# 或者使用智能部署脚本
npm run deploy:cloudflare  # Cloudflare Pages
npm run deploy:vercel     # Vercel
npm run deploy:docker     # Docker
```

### 方法 2: 手动指定平台

```bash
# Cloudflare Pages
npm run build:cloudflare

# Vercel
npm run build:vercel

# Docker
npm run build:docker
```

## ☁️ Cloudflare Pages 部署

### 自动部署
1. 连接 GitHub 仓库到 Cloudflare Pages
2. 设置构建命令：`npm run build:cloudflare`
3. 设置输出目录：`.vercel/output/static`

### 手动部署
```bash
# 构建项目
npm run build:cloudflare

# 部署到 Cloudflare Pages
npx wrangler pages deploy .vercel/output/static
```

### 环境变量
在 Cloudflare Pages 控制台设置：
```
DATABASE_URL=your_database_url
NEXTAUTH_SECRET=your_secret
NEXTAUTH_URL=https://your-domain.pages.dev
STRIPE_PRIVATE_KEY=your_stripe_key
GITHUB_TOKEN=your_github_token
```

## ▲ Vercel 部署

### 自动部署
1. 连接 GitHub 仓库到 Vercel
2. Vercel 会自动检测 Next.js 项目并部署

### 手动部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

## 🐳 Docker 部署

### 构建镜像
```bash
# 构建 Docker 镜像
docker build -t shipsaas-office .

# 运行容器
docker run -p 3000:3000 \
  -e DATABASE_URL="your_database_url" \
  -e NEXTAUTH_SECRET="your_secret" \
  shipsaas-office
```

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=******************************/dbname
      - NEXTAUTH_SECRET=your_secret
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=shipsaas
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🔧 配置文件说明

### `next.config.auto.mjs` (推荐)
自动检测环境并应用相应配置：
- 🔍 自动检测部署平台
- ⚙️ 自动应用最佳配置
- 🚀 零配置部署

### `next.config.cloudflare.mjs`
专门为 Cloudflare Pages 优化：
- ⚡ Edge Runtime 配置
- 🌐 CDN 优化设置
- 📦 减少包大小

### `next.config.mjs`
默认配置，适用于大多数平台。

## 🛠️ 高级配置

### 切换配置文件
```bash
# 使用自动检测配置
cp next.config.auto.mjs next.config.mjs

# 使用 Cloudflare 专用配置
cp next.config.cloudflare.mjs next.config.mjs
```

### 清理手动配置
如果之前手动添加了 edge runtime 配置：
```bash
npm run cleanup:edge
```

## 🔍 故障排除

### Cloudflare Pages 构建失败
1. 确保使用了正确的构建命令：`npm run build:cloudflare`
2. 检查是否有不兼容的 Node.js API
3. 确认所有路由都支持 Edge Runtime

### Vercel 部署问题
1. 检查 `vercel.json` 配置
2. 确认环境变量设置正确
3. 查看构建日志

### Docker 构建问题
1. 确保 Dockerfile 存在
2. 检查依赖项是否完整
3. 验证环境变量配置

## 📊 性能优化

### Cloudflare Pages
- ✅ 自动启用 Edge Runtime
- ✅ 全球 CDN 分发
- ✅ 自动图片优化

### Vercel
- ✅ 无服务器函数
- ✅ 自动扩展
- ✅ 边缘网络

### Docker
- ✅ 容器化隔离
- ✅ 可扩展部署
- ✅ 自托管控制

## 🔗 相关链接

- [Cloudflare Pages 文档](https://developers.cloudflare.com/pages/)
- [Vercel 文档](https://vercel.com/docs)
- [Next.js 部署文档](https://nextjs.org/docs/deployment)
- [Docker 文档](https://docs.docker.com/)

---

💡 **提示**: 推荐使用 `next.config.auto.mjs` 配置，它会自动检测部署环境并应用最佳设置。
