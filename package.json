{"name": "shipsaas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "db:push": "npx dotenv-cli -e .env.local -- npx prisma db push", "db:pull": "npx dotenv-cli -e .env.local -- npx prisma db pull", "db:generate": "npx prisma generate", "db:studio": "npx dotenv-cli -e .env.local -- npx prisma studio", "db:sync": "pnpm run db:pull && pnpm run db:push && pnpm run db:generate", "db:test:push": "npx dotenv-cli -e .env.test -- npx prisma db push", "db:test:init": "node src/__tests__/init-test-db.js", "db:test:studio": "npx dotenv-cli -e .env.test -- npx prisma studio", "postinstall": "prisma generate", "test:db": "pnpm run db:test:init && npx dotenv-cli -e .env.test -- npx jest --config src/__tests__/jest.config.js", "test:db:setup": "pnpm run db:test:push && pnpm run db:generate", "test:db:docker": "docker-compose up -d && npx dotenv-cli -e .env.test -- npx prisma db push && pnpm run test:db && docker-compose down", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"@auth/core": "0.34.2", "@auth/prisma-adapter": "2.4.2", "@number-flow/react": "^0.5.8", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@stripe/stripe-js": "^5.4.0", "@types/google-one-tap": "^1.2.6", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.2", "google-auth-library": "^9.15.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.468.0", "next": "15.3.5", "next-auth": "^4.24.11", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "sonner": "^1.7.1", "stripe": "^17.7.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.5", "@prisma/client": "^6.11.1", "@types/jest": "^29.5.14", "@types/node": "^20.17.10", "@types/react": "^18", "@types/react-dom": "^18", "bcryptjs": "^3.0.2", "dotenv-cli": "^8.0.0", "eslint": "^8", "eslint-config-next": "15.3.5", "jest": "^29.7.0", "postcss": "^8", "prisma": "^6.11.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.1", "typescript": "^5"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}