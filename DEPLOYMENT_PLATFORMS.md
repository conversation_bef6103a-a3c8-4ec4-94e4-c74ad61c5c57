# 🚀 部署平台选择指南

本项目现在支持两个部署平台，每个都有其独特的优势：

## 🔵 Vercel 部署（当前配置）

### ✅ 优势
- **零配置**: 开箱即用的 Next.js 支持
- **自动部署**: Git 推送自动触发部署
- **预览部署**: 每个 PR 都有独立的预览环境
- **边缘函数**: 自动优化的边缘计算
- **分析工具**: 内置性能监控和分析

### 📋 当前状态
- ✅ 已配置 `vercel.json`
- ✅ 使用 `build:vercel` 脚本
- ✅ 支持 NextAuth v4
- ✅ 香港节点优化 (`hkg1`)

### 🛠️ 构建命令
```bash
pnpm run build:vercel
```

## 🟠 Cloudflare Pages 部署（新增支持）

### ✅ 优势
- **全球 CDN**: 更快的全球访问速度
- **成本效益**: 更慷慨的免费额度
- **边缘计算**: 强大的 Workers 平台
- **DDoS 保护**: 企业级安全防护
- **R2 存储**: 兼容 S3 的对象存储

### 📋 当前状态
- ✅ 已配置 OpenNext.js Cloudflare 适配器
- ✅ 使用 `build:cloudflare` 脚本
- ✅ 完美解决 NextAuth 兼容性问题
- ✅ 支持本地预览

### 🛠️ 构建命令
```bash
pnpm run build:cloudflare
```

## 🎯 平台选择建议

### 选择 Vercel 如果：
- 您需要最简单的部署体验
- 您的团队已经熟悉 Vercel 生态系统
- 您需要内置的分析和监控工具
- 您的主要用户在亚太地区（香港节点）

### 选择 Cloudflare Pages 如果：
- 您需要更好的全球性能
- 您希望降低运营成本
- 您需要更强的 DDoS 保护
- 您计划使用 Cloudflare 的其他服务（R2、D1、KV 等）

## 🔄 切换部署平台

### 从 Vercel 切换到 Cloudflare Pages

1. **停用 Vercel 部署**
   ```bash
   # 在 Vercel Dashboard 中暂停项目
   ```

2. **设置 Cloudflare Pages**
   ```bash
   # 连接 GitHub 到 Cloudflare Pages
   # 构建命令: pnpm run build:cloudflare
   # 输出目录: .open-next/worker.js
   ```

3. **更新 DNS**
   ```bash
   # 将域名指向 Cloudflare Pages
   ```

### 从 Cloudflare Pages 切换到 Vercel

1. **重新启用 Vercel 部署**
   ```bash
   # 在 Vercel Dashboard 中重新部署
   ```

2. **更新 DNS**
   ```bash
   # 将域名指向 Vercel
   ```

## 🔧 故障排除

### Vercel 部署问题

如果遇到 `write EPIPE` 错误：
1. 确保 `next.config.mjs` 中的 Cloudflare 初始化被正确条件化
2. 检查 `vercel.json` 中的构建命令
3. 验证环境变量设置

### Cloudflare Pages 部署问题

如果构建失败：
1. 确保所有 edge runtime 配置已移除
2. 检查 `open-next.config.ts` 配置
3. 验证 `wrangler.toml` 设置

## 📊 性能对比

| 特性 | Vercel | Cloudflare Pages |
|------|--------|------------------|
| 全球 CDN | ✅ | ✅ (更多节点) |
| 边缘计算 | ✅ | ✅ (更强大) |
| 免费额度 | 中等 | 慷慨 |
| 构建时间 | 快 | 中等 |
| 冷启动 | 快 | 更快 |
| DDoS 保护 | 基础 | 企业级 |

## 🎉 推荐策略

**当前推荐**: 继续使用 Vercel 进行开发和测试，考虑在生产环境中使用 Cloudflare Pages 以获得更好的性能和成本效益。

**未来规划**: 根据实际使用情况和性能需求，选择最适合的平台作为主要部署目标。
